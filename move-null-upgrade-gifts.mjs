import fs from "fs";
import path from "path";

async function moveNullUpgradeGifts() {
  try {
    // Read the limited gifts file
    const limitedGiftsData = JSON.parse(
      fs.readFileSync("limited-gifts.json", "utf8")
    );

    // Filter gifts where upgradeStars is null
    const nullUpgradeStarsGifts = limitedGiftsData.gifts.filter(
      (gift) => gift.upgradeStars === null
    );

    console.log(
      `\n🔍 Gifts with null upgradeStars (${nullUpgradeStarsGifts.length} total):`
    );
    nullUpgradeStarsGifts.forEach((gift) => {
      console.log(`  ID: ${gift.id}`);
    });

    const originalsPath = "./assets/originals";
    const limitedPath = "./assets/limited";

    // Ensure the limited directory exists
    if (!fs.existsSync(limitedPath)) {
      fs.mkdirSync(limitedPath, { recursive: true });
      console.log(`\n📁 Created directory: ${limitedPath}`);
    }

    let movedCount = 0;
    let notFoundCount = 0;

    console.log("\n🚀 Starting to move folders...\n");

    // Iterate over filtered gifts and move corresponding folders
    for (const gift of nullUpgradeStarsGifts) {
      const giftId = gift.id;
      const sourcePath = path.join(originalsPath, giftId);
      const destinationPath = path.join(limitedPath, giftId);

      // Check if the folder exists in originals
      if (fs.existsSync(sourcePath)) {
        try {
          // Check if destination already exists
          if (fs.existsSync(destinationPath)) {
            console.log(
              `⚠️  Destination already exists for ID: ${giftId} - skipping`
            );
            continue;
          }

          // Move the folder
          fs.renameSync(sourcePath, destinationPath);
          console.log(`✅ Moved: ${giftId} from originals to limited`);
          movedCount++;
        } catch (error) {
          console.error(`❌ Error moving ${giftId}: ${error.message}`);
        }
      } else {
        console.log(`❓ Folder not found in originals: ${giftId}`);
        notFoundCount++;
      }
    }

    console.log("\n📊 Summary:");
    console.log(
      `  Total gifts with null upgradeStars: ${nullUpgradeStarsGifts.length}`
    );
    console.log(`  Successfully moved: ${movedCount}`);
    console.log(`  Not found in originals: ${notFoundCount}`);
    console.log(
      `  Already existed in limited: ${
        nullUpgradeStarsGifts.length - movedCount - notFoundCount
      }`
    );
  } catch (error) {
    console.error("❌ Error:", error.message);
    process.exit(1);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  moveNullUpgradeGifts();
}
