import { TelegramClient, Api } from "telegram";
import { StringSession } from "telegram/sessions/index.js";
import fs from "fs";

const botToken = "7870296231:AAH4uVlMKmpRreydCXCe0AysQ9Jvyf8BEmY"; // e.g., "123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11"
const apiId = 14431416;
const apiHash = "da8fa0a17dd9e0c1b9e420d73a39a710";

const stringSession = new StringSession("");

async function extractLimitedGifts() {
  const client = new TelegramClient(stringSession, apiId, apiHash, {
    connectionRetries: 5,
  });

  try {
    await client.start({
      botAuthToken: botToken,
    });

    const result = await client.invoke(
      new Api.payments.GetStarGifts({ hash: 0 })
    );

    const limitedGifts = result.gifts
      .filter((gift) => gift.limited === true)
      .map((gift) => ({
        id: gift.id,
        limited: gift.limited,
        upgradeStars: gift.upgradeStars,
      }));

    const outputData = {
      timestamp: new Date().toISOString(),
      totalLimitedGifts: limitedGifts.length,
      gifts: limitedGifts,
    };

    fs.writeFileSync("limited-gifts.json", JSON.stringify(outputData, null, 2));
    console.log("\n✅ Limited gifts saved to limited-gifts.json");
  } catch (error) {
    console.error("❌ Error:", error.message);
    process.exit(1);
  } finally {
    await client.disconnect();
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  extractLimitedGifts();
}

export { extractLimitedGifts };
