{"tgs": 1, "v": "5.5.2", "fr": 60, "ip": 0, "op": 180, "w": 512, "h": 512, "nm": "35", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "glow 12", "parent": 64, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 70, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 73, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 74, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 78, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 79, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 83, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 84, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 88, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 93, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 94, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 98, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 99, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 103, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 104, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 108, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 109, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 113, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 114, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 119, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 123, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 124, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 131, "s": [100]}, {"t": 149, "s": [0]}]}, "r": {"a": 0, "k": 221.325}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [146.766, 376.328, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 149, "s": [142.638, 336.532, 0]}]}, "a": {"a": 0, "k": [287.865, 216.468, 0]}, "s": {"a": 0, "k": [123.811, 147.704, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, -78.273], [-83.593, 0], [0, 78.273], [83.593, 0]], "o": [[0, 78.273], [83.593, 0], [0, -78.273], [-83.593, 0]], "v": [[-151.359, 0], [0, 141.726], [151.359, 0], [0, -141.726]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.762, 0.973, 0.941, 0.602, 1, 0.945, 0.882, 0.204, 0.001, 1, 0.401, 0.5, 1, 0]}}, "s": {"a": 0, "k": [0.432, 4.152]}, "e": {"a": 0, "k": [-93.643, -103.027]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "dsfhuyshf35", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [287.865, 216.468]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}], "ip": 70, "op": 150, "st": 2, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "glow 11", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 52, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 61, "s": [78]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 67, "s": [74]}, {"t": 70, "s": [0]}]}, "r": {"a": 0, "k": 90.635}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 52, "s": [394.669, 261.368, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [393.899, 261.83, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [391.372, 266.346, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65, "s": [336.669, 223.368, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 70, "s": [326.669, 159.368, 0]}]}, "a": {"a": 0, "k": [287.865, 216.468, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 52, "s": [115.239, 90.091, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 60, "s": [112.611, 116.917, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 65, "s": [141.84, 88.727, 100]}, {"t": 70, "s": [63.782, 135.324, 100]}]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, -78.273], [-83.593, 0], [0, 78.273], [83.593, 0]], "o": [[0, 78.273], [83.593, 0], [0, -78.273], [-83.593, 0]], "v": [[-151.359, 0], [0, 141.726], [151.359, 0], [0, -141.726]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.001, 1, 1, 1, 0.704, 0.973, 0.941, 0.602, 1, 0.945, 0.882, 0.204, 0.001, 1, 0.401, 0.5, 1, 0]}}, "s": {"a": 0, "k": [0.432, 4.152]}, "e": {"a": 0, "k": [-93.643, -103.027]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "35353535353", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [287.865, 216.468]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}], "ip": 52, "op": 70, "st": -34, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "glow 10", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 52, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 61, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 67, "s": [100]}, {"t": 70, "s": [0]}]}, "r": {"a": 0, "k": 90.635}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 52, "s": [394.669, 261.368, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [393.899, 261.83, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [391.372, 266.346, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65, "s": [336.669, 223.368, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 70, "s": [326.669, 159.368, 0]}]}, "a": {"a": 0, "k": [287.865, 216.468, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 52, "s": [115.239, 90.091, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 60, "s": [165.448, 90.091, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 65, "s": [92.85, 128.269, 100]}, {"t": 70, "s": [63.782, 135.324, 100]}]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, -78.273], [-83.593, 0], [0, 78.273], [83.593, 0]], "o": [[0, 78.273], [83.593, 0], [0, -78.273], [-83.593, 0]], "v": [[-151.359, 0], [0, 141.726], [151.359, 0], [0, -141.726]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.001, 1, 1, 1, 0.704, 0.973, 0.941, 0.602, 1, 0.945, 0.882, 0.204, 0.001, 1, 0.401, 0.5, 1, 0]}}, "s": {"a": 0, "k": [0.432, 4.152]}, "e": {"a": 0, "k": [-93.643, -103.027]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "35opo", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [287.865, 216.468]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}], "ip": 52, "op": 70, "st": -34, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 3, "nm": "midmover", "sr": 1, "ks": {"o": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [256, 256, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 70, "s": [256, 288, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 90, "s": [256, 214, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 115, "s": [256, 328, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 126, "s": [256, 228, 0]}]}}, "ao": 0, "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Orange 6", "sr": 1, "ks": {"r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 121, "s": [153.622]}, {"t": 131, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 121, "s": [8.619, 155.069, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 131, "s": [505.619, 155.069, 0]}]}, "a": {"a": 0, "k": [408.619, 305.069, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 121, "s": [0, 70, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0, 0]}, "t": 126, "s": [70, 70, 100]}, {"t": 131, "s": [0, 70, 100]}]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[7.797, 17.366], [21.619, -17.012], [4.253, 7.443], [10.012, -37.961], [0, 0], [-33.33, -22.505], [-16.657, 21.973], [7.443, 1.772], [-4.253, 35.086], [5.234, -0.688]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-14.937, 10.732], [25.661, 17.327], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.312, -52.163], [-3.656, -14.895], [9.432, -53.995], [-39.814, 11.45], [-41.508, -26.276], [-45.96, 63.728], [54.46, 40.843], [1.339, 45.351], [61.521, -20.773], [16.534, 14.58]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.647058844566, 0.168627455831, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 12}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [408.619, 289.995]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-8.566, -11.82], [-26.244, 7.621]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-15.649, -18.769], [-7.51, 9.359], [28.401, 10.794]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.784313738346, 0.235294118524, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [380.044, 323.943]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-7.011, -18.996], [-23.921, 4.513]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-23.335, -5.96], [-22.238, 27.939], [14.774, 27.939]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.784313738346, 0.235294118524, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [417.745, 277.845]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-28.618, 19.723]], "o": [[14.865, -22.894], [0, 0]], "v": [[-29.905, 36.721], [36.75, -36.218]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.784313738346, 0.235294118524, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [389.458, 315.289]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [389.458, 315.289]}, "a": {"a": 0, "k": [389.458, 315.289]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 30}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[7.797, 17.366], [21.619, -17.012], [4.253, 7.443], [5.316, -32.96], [0, 0], [-33.33, -22.505], [-16.657, 21.973], [7.443, 1.772], [-4.253, 35.086], [5.234, -0.688]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-14.938, 10.732], [25.661, 17.327], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.312, -52.163], [6.579, -14.29], [8.153, -53.995], [-41.094, 12.055], [-43.427, -25.671], [-45.96, 63.728], [53.18, 41.448], [-2.5, 44.746], [62.8, -23.8], [21.651, 15.185]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.443137288094, 0.207843154669, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [408.619, 289.995]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-11.711, 18.036]], "o": [[0, 0], [0, 0]], "v": [[30.316, 4.852], [31.949, -39.321]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.647058844566, 0.168627455831, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [327.605, 391.332]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5", "bm": 0, "hd": false}], "ip": 121, "op": 132, "st": 52, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Orange 4", "sr": 1, "ks": {"r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 99, "s": [153.622]}, {"t": 109, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 99, "s": [8.619, 199.069, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 109, "s": [505.619, 199.069, 0]}]}, "a": {"a": 0, "k": [408.619, 305.069, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 99, "s": [0, 70, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0, 0]}, "t": 104, "s": [70, 70, 100]}, {"t": 109, "s": [0, 70, 100]}]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[7.797, 17.366], [21.619, -17.012], [4.253, 7.443], [10.012, -37.961], [0, 0], [-33.33, -22.505], [-16.657, 21.973], [7.443, 1.772], [-4.253, 35.086], [5.234, -0.688]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-14.937, 10.732], [25.661, 17.327], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.312, -52.163], [-3.656, -14.895], [9.432, -53.995], [-39.814, 11.45], [-41.508, -26.276], [-45.96, 63.728], [54.46, 40.843], [1.339, 45.351], [61.521, -20.773], [16.534, 14.58]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.647058844566, 0.168627455831, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 12}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [408.619, 289.995]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-8.566, -11.82], [-26.244, 7.621]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-15.649, -18.769], [-7.51, 9.359], [28.401, 10.794]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.784313738346, 0.235294118524, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [380.044, 323.943]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-7.011, -18.996], [-23.921, 4.513]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-23.335, -5.96], [-22.238, 27.939], [14.774, 27.939]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.784313738346, 0.235294118524, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [417.745, 277.845]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-28.618, 19.723]], "o": [[14.865, -22.894], [0, 0]], "v": [[-29.905, 36.721], [36.75, -36.218]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.784313738346, 0.235294118524, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [389.458, 315.289]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [389.458, 315.289]}, "a": {"a": 0, "k": [389.458, 315.289]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 30}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[7.797, 17.366], [21.619, -17.012], [4.253, 7.443], [5.316, -32.96], [0, 0], [-33.33, -22.505], [-16.657, 21.973], [7.443, 1.772], [-4.253, 35.086], [5.234, -0.688]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-14.938, 10.732], [25.661, 17.327], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.312, -52.163], [6.579, -14.29], [8.153, -53.995], [-41.094, 12.055], [-43.427, -25.671], [-45.96, 63.728], [53.18, 41.448], [-2.5, 44.746], [62.8, -23.8], [21.651, 15.185]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.443137288094, 0.207843154669, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [408.619, 289.995]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-11.711, 18.036]], "o": [[0, 0], [0, 0]], "v": [[30.316, 4.852], [31.949, -39.321]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.647058844566, 0.168627455831, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [327.605, 391.332]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5", "bm": 0, "hd": false}], "ip": 99, "op": 110, "st": 30, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Orange 2", "sr": 1, "ks": {"r": {"a": 0, "k": 177.876}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 79, "s": [8.619, 114.069, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 86, "s": [505.619, 114.069, 0]}]}, "a": {"a": 0, "k": [408.619, 305.069, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 79, "s": [0, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0, 0]}, "t": 82.5, "s": [100, 100, 100]}, {"t": 86, "s": [0, 100, 100]}]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[7.797, 17.366], [21.619, -17.012], [4.253, 7.443], [10.012, -37.961], [0, 0], [-33.33, -22.505], [-16.657, 21.973], [7.443, 1.772], [-4.253, 35.086], [5.234, -0.688]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-14.937, 10.732], [25.661, 17.327], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.312, -52.163], [-3.656, -14.895], [9.432, -53.995], [-39.814, 11.45], [-41.508, -26.276], [-45.96, 63.728], [54.46, 40.843], [1.339, 45.351], [61.521, -20.773], [16.534, 14.58]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.647058844566, 0.168627455831, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 12}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [408.619, 289.995]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-8.566, -11.82], [-26.244, 7.621]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-15.649, -18.769], [-7.51, 9.359], [28.401, 10.794]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.784313738346, 0.235294118524, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [380.044, 323.943]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-7.011, -18.996], [-23.921, 4.513]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-23.335, -5.96], [-22.238, 27.939], [14.774, 27.939]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.784313738346, 0.235294118524, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [417.745, 277.845]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-28.618, 19.723]], "o": [[14.865, -22.894], [0, 0]], "v": [[-29.905, 36.721], [36.75, -36.218]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.784313738346, 0.235294118524, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [389.458, 315.289]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [389.458, 315.289]}, "a": {"a": 0, "k": [389.458, 315.289]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 30}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[7.797, 17.366], [21.619, -17.012], [4.253, 7.443], [5.316, -32.96], [0, 0], [-33.33, -22.505], [-16.657, 21.973], [7.443, 1.772], [-4.253, 35.086], [5.234, -0.688]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-14.938, 10.732], [25.661, 17.327], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.312, -52.163], [6.579, -14.29], [8.153, -53.995], [-41.094, 12.055], [-43.427, -25.671], [-45.96, 63.728], [53.18, 41.448], [-2.5, 44.746], [62.8, -23.8], [21.651, 15.185]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.443137288094, 0.207843154669, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [408.619, 289.995]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-11.711, 18.036]], "o": [[0, 0], [0, 0]], "v": [[30.316, 4.852], [31.949, -39.321]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.647058844566, 0.168627455831, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [327.605, 391.332]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5", "bm": 0, "hd": false}], "ip": 79, "op": 86, "st": 10, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "speedfront 5", "sr": 1, "ks": {"o": {"a": 0, "k": 50}, "p": {"a": 0, "k": [5, 458.736, 0]}, "a": {"a": 0, "k": [-251, 42, 0]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-251, 42], [249.5, 42]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 73, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 77.666, "s": [100]}, {"t": 79, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 93.666, "s": [100]}, {"t": 95, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 104, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 108.666, "s": [100]}, {"t": 110, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 122.666, "s": [100]}, {"t": 124, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 136, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 140.666, "s": [100]}, {"t": 142, "s": [100], "h": 1}]}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 73, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 74.334, "s": [0]}, {"t": 79, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90.334, "s": [0]}, {"t": 95, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 104, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105.334, "s": [0]}, {"t": 110, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 119.334, "s": [0]}, {"t": 124, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 136, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 137.334, "s": [0]}, {"t": 142, "s": [100], "h": 1}]}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 35}, "w": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 73, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 76, "s": [20]}, {"t": 79, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 92, "s": [20]}, {"t": 95, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 104, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 107, "s": [20]}, {"t": 110, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 121, "s": [20]}, {"t": 124, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 136, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 139, "s": [20]}, {"t": 142, "s": [0], "h": 1}]}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Shape 1", "bm": 0, "hd": false}], "ip": 73, "op": 143, "st": 9, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "speedfront 4", "sr": 1, "ks": {"o": {"a": 0, "k": 50}, "p": {"a": 0, "k": [5, 378.736, 0]}, "a": {"a": 0, "k": [-251, 42, 0]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-251, 42], [249.5, 42]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 62, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 66.666, "s": [100]}, {"t": 68, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 78, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 82.666, "s": [100]}, {"t": 84, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 93, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 97.666, "s": [100]}, {"t": 99, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 107, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 111.666, "s": [100]}, {"t": 113, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 125, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 129.666, "s": [100]}, {"t": 131, "s": [100], "h": 1}]}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 62, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 65, "s": [0]}, {"t": 68, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 78, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 81, "s": [0]}, {"t": 84, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 93, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 96, "s": [0]}, {"t": 99, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 107, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110, "s": [0]}, {"t": 113, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 125, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 128, "s": [0]}, {"t": 131, "s": [100], "h": 1}]}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 35}, "w": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 62, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 65, "s": [20]}, {"t": 68, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 78, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 81, "s": [20]}, {"t": 84, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 93, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 96, "s": [20]}, {"t": 99, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 107, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 110, "s": [20]}, {"t": 113, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 125, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 128, "s": [20]}, {"t": 131, "s": [0], "h": 1}]}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Shape 1", "bm": 0, "hd": false}], "ip": 62, "op": 132, "st": -2, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "speedfront 3", "sr": 1, "ks": {"o": {"a": 0, "k": 50}, "p": {"a": 0, "k": [5, 58.736, 0]}, "a": {"a": 0, "k": [-251, 42, 0]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-251, 42], [249.5, 42]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 65, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 69.666, "s": [100]}, {"t": 71, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 81, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 85.666, "s": [100]}, {"t": 87, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 96, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100.666, "s": [100]}, {"t": 102, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 114.666, "s": [100]}, {"t": 116, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 128, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 132.666, "s": [100]}, {"t": 134, "s": [100], "h": 1}]}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 65, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 68, "s": [0]}, {"t": 71, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 81, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 84, "s": [0]}, {"t": 87, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 96, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 99, "s": [0]}, {"t": 102, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 113, "s": [0]}, {"t": 116, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 128, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 131, "s": [0]}, {"t": 134, "s": [100], "h": 1}]}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 35}, "w": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 65, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 68, "s": [20]}, {"t": 71, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 81, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 84, "s": [20]}, {"t": 87, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 96, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 99, "s": [20]}, {"t": 102, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 113, "s": [20]}, {"t": 116, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 128, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 131, "s": [20]}, {"t": 134, "s": [0], "h": 1}]}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Shape 1", "bm": 0, "hd": false}], "ip": 65, "op": 135, "st": 1, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "speedfront 2", "sr": 1, "ks": {"o": {"a": 0, "k": 50}, "p": {"a": 0, "k": [5, 118.736, 0]}, "a": {"a": 0, "k": [-251, 42, 0]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-251, 42], [249.5, 42]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 64.666, "s": [100]}, {"t": 66, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 76, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80.666, "s": [100]}, {"t": 82, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 91, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 95.666, "s": [100]}, {"t": 97, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 109.666, "s": [100]}, {"t": 111, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 123, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 127.666, "s": [100]}, {"t": 129, "s": [100], "h": 1}]}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 63, "s": [0]}, {"t": 66, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 76, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 79, "s": [0]}, {"t": 82, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 91, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 94, "s": [0]}, {"t": 97, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 108, "s": [0]}, {"t": 111, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 123, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 126, "s": [0]}, {"t": 129, "s": [100], "h": 1}]}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 35}, "w": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 63, "s": [20]}, {"t": 66, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 76, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 79, "s": [20]}, {"t": 82, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 91, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 94, "s": [20]}, {"t": 97, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 108, "s": [20]}, {"t": 111, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 123, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 126, "s": [20]}, {"t": 129, "s": [0], "h": 1}]}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Shape 1", "bm": 0, "hd": false}], "ip": 60, "op": 130, "st": -4, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 3, "nm": "sparks", "parent": 57, "sr": 1, "ks": {"o": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-37.552, 36.304, 0]}, "s": {"a": 0, "k": [134.113, 134.113, 100]}}, "ao": 0, "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "fx 55", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 92.4, "s": [100]}, {"t": 94, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 87, "s": [73.678, -149.238, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 93, "s": [243.678, -149.238, 0]}]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 88, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 89.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 91.6, "s": [169, 169]}, {"t": 94, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 88, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 91.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 94, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[9.293, 2.113], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-5.453, 1.887], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 87, "op": 95, "st": 85, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "fx 54", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89.4, "s": [100]}, {"t": 91, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [73.678, -84.768, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [243.678, -84.768, 0]}]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 85, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 86.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 88.6, "s": [169, 169]}, {"t": 91, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 85, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 88.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 91, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[9.293, 2.113], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-5.453, 1.887], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 84, "op": 92, "st": 82, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "fx 53", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 104.4, "s": [100]}, {"t": 106, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 99, "s": [73.678, -92.941, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 105, "s": [243.678, -92.941, 0]}]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 100, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 101.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 103.6, "s": [169, 169]}, {"t": 106, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 100, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 101.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 103.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 106, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[9.293, 2.113], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-5.453, 1.887], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 99, "op": 107, "st": 97, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "fx 52", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 68.4, "s": [100]}, {"t": 70, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [73.678, -9.056, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 69, "s": [243.678, -9.056, 0]}]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 64, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 65.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 67.6, "s": [169, 169]}, {"t": 70, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 64, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 67.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 70, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[9.293, 2.113], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-5.453, 1.887], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 63, "op": 71, "st": 61, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "fx 51", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 113.4, "s": [100]}, {"t": 115, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [73.678, -37.018, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 114, "s": [243.678, -37.018, 0]}]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 109, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 110.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 112.6, "s": [169, 169]}, {"t": 115, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 109, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 112.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 115, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[9.293, 2.113], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-5.453, 1.887], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 108, "op": 116, "st": 106, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "fx 50", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110.4, "s": [100]}, {"t": 112, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 105, "s": [73.678, 46.867, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 111, "s": [243.678, 46.867, 0]}]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 106, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 107.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 109.6, "s": [169, 169]}, {"t": 112, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 106, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 109.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 112, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[9.293, 2.113], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-5.453, 1.887], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 105, "op": 113, "st": 103, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 4, "nm": "fx 49", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80.4, "s": [100]}, {"t": 82, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [73.678, -120.808, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 81, "s": [243.678, -120.808, 0]}]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 76, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 77.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 79.6, "s": [169, 169]}, {"t": 82, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 76, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 79.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 82, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[9.293, 2.113], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-5.453, 1.887], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 75, "op": 83, "st": 73, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 4, "nm": "fx 48", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 86.4, "s": [100]}, {"t": 88, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [73.678, -153.912, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 87, "s": [243.678, -153.912, 0]}]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 82, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 83.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 85.6, "s": [169, 169]}, {"t": 88, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 82, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 85.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 88, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[9.293, 2.113], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-5.453, 1.887], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 81, "op": 89, "st": 79, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 4, "nm": "fx 47", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 119.4, "s": [100]}, {"t": 121, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 114, "s": [73.678, 18.906, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 120, "s": [243.678, 18.906, 0]}]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 115, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 116.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 118.6, "s": [169, 169]}, {"t": 121, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 115, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 118.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 121, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[9.293, 2.113], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-5.453, 1.887], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 114, "op": 122, "st": 112, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 4, "nm": "fx 46", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 125.4, "s": [100]}, {"t": 127, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [73.678, -111.582, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 126, "s": [243.678, -111.582, 0]}]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 121, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 122.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 124.6, "s": [169, 169]}, {"t": 127, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 121, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 122.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 124.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 127, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[9.293, 2.113], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-5.453, 1.887], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 120, "op": 128, "st": 118, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 4, "nm": "fx 45", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 122.4, "s": [100]}, {"t": 124, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 117, "s": [73.678, -83.62, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 123, "s": [243.678, -83.62, 0]}]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 118, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 119.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 121.6, "s": [169, 169]}, {"t": 124, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 118, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 119.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 121.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 124, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[9.293, 2.113], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-5.453, 1.887], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 117, "op": 125, "st": 115, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 4, "nm": "fx 44", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 101.4, "s": [100]}, {"t": 103, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 96, "s": [73.678, 18.906, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 102, "s": [243.678, 18.906, 0]}]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 97, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 98.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 100.6, "s": [169, 169]}, {"t": 103, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 97, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 100.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 103, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[9.293, 2.113], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-5.453, 1.887], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 96, "op": 104, "st": 94, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 4, "nm": "fx 43", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 98.4, "s": [100]}, {"t": 100, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [73.678, -37.018, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 99, "s": [243.678, -37.018, 0]}]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 94, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 95.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 97.6, "s": [169, 169]}, {"t": 100, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 94, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 97.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 100, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[9.293, 2.113], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-5.453, 1.887], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 93, "op": 101, "st": 91, "bm": 0}, {"ddd": 0, "ind": 26, "ty": 4, "nm": "fx 42", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 128.4, "s": [100]}, {"t": 130, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 123, "s": [73.678, -64.979, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 129, "s": [243.678, -64.979, 0]}]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 124, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 125.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 127.6, "s": [169, 169]}, {"t": 130, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 124, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 125.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 127.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 130, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[9.293, 2.113], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-5.453, 1.887], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 123, "op": 131, "st": 121, "bm": 0}, {"ddd": 0, "ind": 27, "ty": 4, "nm": "fx 41", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 95.4, "s": [100]}, {"t": 97, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [73.678, -74.3, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 96, "s": [243.678, -74.3, 0]}]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 91, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 92.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 94.6, "s": [169, 169]}, {"t": 97, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 91, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 94.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 97, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[9.293, 2.113], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-5.453, 1.887], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 90, "op": 98, "st": 88, "bm": 0}, {"ddd": 0, "ind": 28, "ty": 4, "nm": "fx 40", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 71.4, "s": [100]}, {"t": 73, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [73.678, -30.36, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 72, "s": [243.678, -30.36, 0]}]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 67, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 68.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 70.6, "s": [169, 169]}, {"t": 73, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 67, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 70.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 73, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[9.293, 2.113], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-5.453, 1.887], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 66, "op": 74, "st": 64, "bm": 0}, {"ddd": 0, "ind": 29, "ty": 4, "nm": "fx 39", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 107.4, "s": [100]}, {"t": 109, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [73.678, -9.056, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 108, "s": [243.678, -9.056, 0]}]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 103, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 104.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 106.6, "s": [169, 169]}, {"t": 109, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 103, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 106.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 109, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[9.293, 2.113], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-5.453, 1.887], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 102, "op": 110, "st": 100, "bm": 0}, {"ddd": 0, "ind": 30, "ty": 4, "nm": "fx 38", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 74.4, "s": [100]}, {"t": 76, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69, "s": [73.678, -83.127, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 75, "s": [243.678, -83.127, 0]}]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 70, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 71.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 73.6, "s": [169, 169]}, {"t": 76, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 70, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 73.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 76, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[9.293, 2.113], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-5.453, 1.887], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 69, "op": 77, "st": 67, "bm": 0}, {"ddd": 0, "ind": 31, "ty": 4, "nm": "fx 37", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 77.4, "s": [100]}, {"t": 79, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [73.678, -29.859, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 78, "s": [243.678, -29.859, 0]}]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 73, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 74.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 76.6, "s": [169, 169]}, {"t": 79, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 73, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 76.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 79, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[9.293, 2.113], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-5.453, 1.887], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 72, "op": 80, "st": 70, "bm": 0}, {"ddd": 0, "ind": 32, "ty": 4, "nm": "fx 36", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 116.4, "s": [100]}, {"t": 118, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 111, "s": [73.678, -9.056, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 117, "s": [243.678, -9.056, 0]}]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 112, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 113.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 115.6, "s": [169, 169]}, {"t": 118, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 112, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 115.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 118, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[9.293, 2.113], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-5.453, 1.887], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 111, "op": 119, "st": 109, "bm": 0}, {"ddd": 0, "ind": 33, "ty": 4, "nm": "fx 35", "parent": 12, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 68.4, "s": [100]}, {"t": 70, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [73.678, 33.944, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 69, "s": [243.678, 33.944, 0]}]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 64, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 65.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 67.6, "s": [169, 169]}, {"t": 70, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 64, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 67.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 70, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[9.293, 2.113], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-5.453, 1.887], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 63, "op": 71, "st": 61, "bm": 0}, {"ddd": 0, "ind": 34, "ty": 4, "nm": "fx 69", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 170.4, "s": [100]}, {"t": 172, "s": [0]}]}, "p": {"a": 0, "k": [247.678, 358.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [72.846, 72.846, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 162, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 163.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 165.6, "s": [169, 169]}, {"t": 172, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 162, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 163.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 165.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 172, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 161, "op": 172, "st": 159, "bm": 0}, {"ddd": 0, "ind": 35, "ty": 4, "nm": "fx 68", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 176.4, "s": [100]}, {"t": 178, "s": [0]}]}, "p": {"a": 0, "k": [415.678, 349.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [72.846, 72.846, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 168, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 169.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 171.6, "s": [169, 169]}, {"t": 178, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 168, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 169.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 171.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 178, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 167, "op": 178, "st": 165, "bm": 0}, {"ddd": 0, "ind": 36, "ty": 4, "nm": "fx 67", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 169.4, "s": [100]}, {"t": 171, "s": [0]}]}, "p": {"a": 0, "k": [338.678, 445.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [123.972, 123.972, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 161, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 162.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 164.6, "s": [169, 169]}, {"t": 171, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 161, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 162.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 164.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 171, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 160, "op": 171, "st": 158, "bm": 0}, {"ddd": 0, "ind": 37, "ty": 4, "nm": "fx 66", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 168.4, "s": [100]}, {"t": 170, "s": [0]}]}, "p": {"a": 0, "k": [379.678, 405.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [86.399, 86.399, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 160, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 161.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 163.6, "s": [169, 169]}, {"t": 170, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 160, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 161.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 163.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 170, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 159, "op": 170, "st": 157, "bm": 0}, {"ddd": 0, "ind": 38, "ty": 4, "nm": "fx 65", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 164.4, "s": [100]}, {"t": 166, "s": [0]}]}, "p": {"a": 0, "k": [308.678, 409.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [113.179, 113.179, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 156, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 157.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 159.6, "s": [169, 169]}, {"t": 166, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 156, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 157.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 159.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 166, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 155, "op": 166, "st": 153, "bm": 0}, {"ddd": 0, "ind": 39, "ty": 4, "nm": "fx 64", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 160.4, "s": [100]}, {"t": 162, "s": [0]}]}, "p": {"a": 0, "k": [257.678, 440.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [83.964, 83.964, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 152, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 153.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 155.6, "s": [169, 169]}, {"t": 162, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 152, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 153.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 155.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 162, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 151, "op": 162, "st": 149, "bm": 0}, {"ddd": 0, "ind": 40, "ty": 4, "nm": "fx 63", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 154.4, "s": [100]}, {"t": 156, "s": [0]}]}, "p": {"a": 0, "k": [224.678, 407.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [135.38, 135.38, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 146, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 147.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 149.6, "s": [169, 169]}, {"t": 156, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 146, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 147.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 149.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 156, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 145, "op": 156, "st": 143, "bm": 0}, {"ddd": 0, "ind": 41, "ty": 4, "nm": "fx 62", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 153.4, "s": [100]}, {"t": 155, "s": [0]}]}, "p": {"a": 0, "k": [156.678, 345.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [135.38, 135.38, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 145, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 146.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 148.6, "s": [169, 169]}, {"t": 155, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 145, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 146.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 148.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 155, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 144, "op": 155, "st": 142, "bm": 0}, {"ddd": 0, "ind": 42, "ty": 4, "nm": "fx 61", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 149.4, "s": [100]}, {"t": 151, "s": [0]}]}, "p": {"a": 0, "k": [384.678, 365.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [135.38, 135.38, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 141, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 142.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 144.6, "s": [169, 169]}, {"t": 151, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 141, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 142.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 144.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 151, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 140, "op": 151, "st": 138, "bm": 0}, {"ddd": 0, "ind": 43, "ty": 4, "nm": "fx 60", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 147.4, "s": [100]}, {"t": 149, "s": [0]}]}, "p": {"a": 0, "k": [322.678, 424.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [135.38, 135.38, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 139, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 140.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 142.6, "s": [169, 169]}, {"t": 149, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 139, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 140.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 142.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 149, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 138, "op": 149, "st": 136, "bm": 0}, {"ddd": 0, "ind": 44, "ty": 4, "nm": "fx 59", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 146.4, "s": [100]}, {"t": 148, "s": [0]}]}, "p": {"a": 0, "k": [191.678, 407.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [135.38, 135.38, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 138, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 139.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 141.6, "s": [169, 169]}, {"t": 148, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 138, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 139.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 141.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 148, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 137, "op": 148, "st": 135, "bm": 0}, {"ddd": 0, "ind": 45, "ty": 4, "nm": "fx 58", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 145.4, "s": [100]}, {"t": 147, "s": [0]}]}, "p": {"a": 0, "k": [262.678, 370.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [135.38, 135.38, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 137, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 138.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 140.6, "s": [169, 169]}, {"t": 147, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 137, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 138.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 140.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 147, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 136, "op": 147, "st": 134, "bm": 0}, {"ddd": 0, "ind": 46, "ty": 4, "nm": "fx 57", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 143.4, "s": [100]}, {"t": 145, "s": [0]}]}, "p": {"a": 0, "k": [262.678, 407.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [135.38, 135.38, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 135, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 136.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 138.6, "s": [169, 169]}, {"t": 145, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 135, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 136.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 138.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 145, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 134, "op": 145, "st": 132, "bm": 0}, {"ddd": 0, "ind": 47, "ty": 4, "nm": "fx 56", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 141.4, "s": [100]}, {"t": 143, "s": [0]}]}, "p": {"a": 0, "k": [324.678, 368.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [135.38, 135.38, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 133, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 134.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 136.6, "s": [169, 169]}, {"t": 143, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 133, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 134.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 136.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 143, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 132, "op": 143, "st": 130, "bm": 0}, {"ddd": 0, "ind": 48, "ty": 4, "nm": "fx 34", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 29.4, "s": [100]}, {"t": 31, "s": [0]}]}, "p": {"a": 0, "k": [395.678, 348.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 21, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 22.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 24.6, "s": [169, 169]}, {"t": 31, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 21, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 24.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 31, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 20, "op": 31, "st": 18, "bm": 0}, {"ddd": 0, "ind": 49, "ty": 4, "nm": "fx 33", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 27.4, "s": [100]}, {"t": 29, "s": [0]}]}, "p": {"a": 0, "k": [379.678, 277.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 19, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 20.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 22.6, "s": [169, 169]}, {"t": 29, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 19, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 22.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 29, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 18, "op": 29, "st": 16, "bm": 0}, {"ddd": 0, "ind": 50, "ty": 4, "nm": "fx 30", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 24.4, "s": [100]}, {"t": 26, "s": [0]}]}, "p": {"a": 0, "k": [357.678, 172.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [121.092, 121.092, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 16, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 17.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 19.6, "s": [169, 169]}, {"t": 26, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 19.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 26, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 15, "op": 26, "st": 13, "bm": 0}, {"ddd": 0, "ind": 51, "ty": 4, "nm": "fx 31", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23.4, "s": [100]}, {"t": 25, "s": [0]}]}, "p": {"a": 0, "k": [281.678, 116.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [135.38, 135.38, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 15, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 16.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 18.6, "s": [169, 169]}, {"t": 25, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 18.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 14, "op": 25, "st": 12, "bm": 0}, {"ddd": 0, "ind": 52, "ty": 4, "nm": "fx 29", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23.4, "s": [100]}, {"t": 25, "s": [0]}]}, "p": {"a": 0, "k": [259.678, 72.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 15, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 16.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 18.6, "s": [169, 169]}, {"t": 25, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 18.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 25, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 14, "op": 25, "st": 12, "bm": 0}, {"ddd": 0, "ind": 53, "ty": 4, "nm": "fx 32", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19.4, "s": [100]}, {"t": 21, "s": [0]}]}, "r": {"a": 0, "k": -50.862}, "p": {"a": 0, "k": [122.678, 144.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [122.489, 122.489, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 11, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 12.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 14.6, "s": [169, 169]}, {"t": 21, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 11, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 14.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 21, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 10, "op": 21, "st": 8, "bm": 0}, {"ddd": 0, "ind": 54, "ty": 4, "nm": "fx 28", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 19.4, "s": [100]}, {"t": 21, "s": [0]}]}, "p": {"a": 0, "k": [97.678, 182.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 11, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 12.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 14.6, "s": [169, 169]}, {"t": 21, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 11, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 14.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 21, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 10, "op": 21, "st": 8, "bm": 0}, {"ddd": 0, "ind": 55, "ty": 4, "nm": "fx 27", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 14.4, "s": [100]}, {"t": 16, "s": [0]}]}, "p": {"a": 0, "k": [141.678, 352.944, 0]}, "a": {"a": 0, "k": [670.508, 243.277, 0]}, "s": {"a": 0, "k": [200, 200, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.65, -5.311], [-5.311, -5.65], [-5.65, 5.31], [5.31, 5.65]], "o": [[-5.65, 5.311], [5.311, 5.65], [5.65, -5.31], [-5.31, -5.65]], "v": [[-10.329, -9.969], [-10.944, 9.878], [8.902, 10.493], [9.518, -9.353]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.158, 0.992, 1, 0.518, 0.393, 0.988, 1, 0.259, 0.721, 0.984, 1, 0, 0.158, 1, 0.393, 0.5, 0.721, 0]}}, "s": {"a": 0, "k": [-0.353, 0]}, "e": {"a": 0, "k": [8.432, 15.062]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "Gradient Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [671.477, 244.809]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 6, "s": [0, 0]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 7.6, "s": [147, 147]}, {"i": {"x": [0.4, 0.4], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 9.6, "s": [169, 169]}, {"t": 16, "s": [0, 0]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.456, 1.86], [0.46, 1.851], [0.467, 1.848], [0.476, 1.851], [0.48, 1.858], [0.477, 1.867], [0.469, 1.871], [0.46, 1.868]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7.6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.405, 1.837], [3.437, 5.706], [0.407, 7.15], [-3.398, 5.742], [-5.386, 2.351], [-3.912, -1.241], [-0.675, -3.264], [3.385, -2.037]], "c": true}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 9.6, "s": [{"i": [[0, 0], [2.422, -2.478], [0, 0], [2.685, 1.913], [0, 0], [-1.35, 1.395], [0.727, 20.687], [-2.078, -2.349]], "o": [[0, 0], [-2.422, 2.478], [0, 0], [-2.685, -1.913], [0, 0], [1.35, -1.395], [0, 0], [2.078, 2.349]], "v": [[23.072, 1.879], [2.77, 4.165], [1.324, 28.983], [-2.232, 4.325], [-24.261, 0.767], [-2.996, -0.533], [-0.467, -28.598], [2.801, -2.037]], "c": true}]}, {"t": 16, "s": [{"i": [[0, 0], [0.052, -0.054], [0, 0], [0.058, 0.041], [0, 0], [-0.029, 0.03], [0.043, 20.392], [-0.045, -0.051]], "o": [[0, 0], [-0.052, 0.054], [0, 0], [-0.058, -0.041], [0, 0], [0.029, -0.03], [0, 0], [0.045, 0.051]], "v": [[42.738, 2.045], [0.862, 1.944], [0.324, 55.983], [0.753, 1.948], [-49.428, 1.267], [0.737, 1.843], [0.867, -52.598], [0.862, 1.81]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313785329, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [670.508, 243.277]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}], "ip": 5, "op": 16, "st": 3, "bm": 0}, {"ddd": 0, "ind": 56, "ty": 3, "nm": "rem", "sr": 1, "ks": {"o": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [256, 256, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [281, 256, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13, "s": [307, 258, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [307, 318, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [249, 288, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 35, "s": [256, 256, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 55, "s": [244, 256, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 89, "s": [244, 256, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.74}, "o": {"x": 0.333, "y": 0}, "t": 123, "s": [244, 256, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0.252}, "t": 135, "s": [270.128, 215.728, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 145, "s": [290, 252, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 179, "s": [256, 256, 0]}]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.167]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [95, 95, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 10, "s": [85, 85, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 13, "s": [85, 85, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 35, "s": [85, 85, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 55, "s": [95, 95, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 66, "s": [70, 70, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 89, "s": [80, 80, 100]}, {"i": {"x": [0.34, 0.34, 0.34], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 123, "s": [80, 80, 100]}, {"t": 179, "s": [95, 95, 100]}]}}, "ao": 0, "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 57, "ty": 3, "nm": "broom updown", "parent": 56, "sr": 1, "ks": {"o": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"i": {"x": 0.12, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 35, "s": [0, 0, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.88, "y": 0}, "t": 50, "s": [0, 6, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 70, "s": [0, -106, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 90, "s": [0, 154, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 115, "s": [0, -106, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 126, "s": [0, 154, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 145, "s": [0, 0, 0]}]}}, "ao": 0, "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 58, "ty": 3, "nm": "broommaster", "parent": 57, "sr": 1, "ks": {"o": {"a": 0, "k": 0}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.66], "y": [0]}, "t": 0, "s": [-51.344]}, {"i": {"x": [0.34], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 25, "s": [212.485]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.217], "y": [0]}, "t": 35, "s": [175.315]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [184.315]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 61, "s": [193.25]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 80, "s": [150.261]}, {"i": {"x": [0.809], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 104, "s": [190.73]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 122, "s": [154.088]}, {"i": {"x": [0.12], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 145, "s": [322.759]}, {"t": 179, "s": [308.656]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.34, "y": 1}, "o": {"x": 0.33, "y": 0}, "t": 0, "s": [37, -61, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.33, "y": 0}, "t": 35, "s": [-51, -21, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.88, "y": 0}, "t": 55, "s": [61, -21, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 66, "s": [-79, -21, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 90, "s": [-51, -21, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 130, "s": [-51, -21, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 179, "s": [37, -61, 0]}]}, "s": {"a": 1, "k": [{"i": {"x": [0.34, 0.34, 0.34], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.649, 0.649, 0.649], "y": [1, 1, 1]}, "o": {"x": [0.312, 0.312, 0.312], "y": [0, 0, 0]}, "t": 35, "s": [90, 90, 100]}, {"i": {"x": [0.34, 0.34, 0.34], "y": [1, 1, 1]}, "o": {"x": [1, 1, 1], "y": [0, 0, 0]}, "t": 55, "s": [66, 111, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.27, 0.27, 0.27], "y": [0, 0, 0]}, "t": 66, "s": [100, 80, 100]}, {"i": {"x": [0.832, 0.832, 0.832], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 77, "s": [90, 90, 100]}, {"i": {"x": [0.34, 0.34, 0.34], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 130, "s": [90, 90, 100]}, {"i": {"x": [0.12, 0.12, 0.12], "y": [1, 1, 1]}, "o": {"x": [0.33, 0.33, 0.33], "y": [0, 0, 0]}, "t": 145, "s": [90, 90, 100]}, {"t": 179, "s": [100, 100, 100]}]}}, "ao": 0, "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 59, "ty": 4, "nm": "woosh 5", "parent": 65, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 63, "s": [60]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 75, "s": [60]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80, "s": [0]}, {"t": 81, "s": [50]}]}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 63, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 74, "s": [0]}, {"t": 85, "s": [10.538]}]}, "p": {"a": 0, "k": [290.226, 205.03, 0]}, "a": {"a": 0, "k": [-57.407, -114.607, 0]}, "s": {"a": 0, "k": [68.139, 68.139, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-39.711, -84.966], [-64.264, 118.572], [-325.533, -57.029], [-86.379, -139.731]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [{"i": [[-5.967, -34.501], [0, 0], [0, 0], [-151.967, -45.71]], "o": [[16.282, 94.143], [0, 0], [0, 0], [33.53, 10.085]], "v": [[-30.041, -76.491], [-7.796, 233.345], [-459.939, -130.829], [-112.791, -145.967]], "c": true}]}, {"t": 81, "s": [{"i": [[0, 0], [85.099, -40.512], [-16.885, 56.203], [0, 0]], "o": [[0, 0], [-52.987, 25.225], [27.244, -90.685], [0, 0]], "v": [[-17.101, -120.84], [-109.398, 216.586], [-349.286, 3.974], [-62.19, -155.843]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.002, 1, 1, 1, 0.48, 1, 1, 1, 0.964, 1, 1, 1, 0, 1, 0.478, 0.5, 0.964, 0]}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [-15.256, -173.82], "to": [0, 0], "ti": [0, 0]}, {"t": 81, "s": [-66.422, -104.101]}]}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [-48.463, -128.59], "to": [0, 0], "ti": [0, 0]}, {"t": 81, "s": [-283.384, 117.82]}]}, "t": 1, "nm": "35uhilsdfs", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}], "ip": 63, "op": 82, "st": 13, "bm": 0}, {"ddd": 0, "ind": 60, "ty": 4, "nm": "woosh 6", "parent": 65, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 62, "s": [60]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 74, "s": [60]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 79, "s": [0]}, {"t": 80, "s": [50]}]}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 62, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 73, "s": [0]}, {"t": 84, "s": [10.538]}]}, "p": {"a": 0, "k": [424.665, 80.591, 0]}, "a": {"a": 0, "k": [-57.407, -114.607, 0]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-39.711, -84.966], [-64.264, 118.572], [-325.533, -57.029], [-86.379, -139.731]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [{"i": [[-5.967, -34.501], [0, 0], [0, 0], [-151.967, -45.71]], "o": [[16.282, 94.143], [0, 0], [0, 0], [33.53, 10.085]], "v": [[-30.041, -76.491], [-7.796, 233.345], [-459.939, -130.829], [-112.791, -145.967]], "c": true}]}, {"t": 80, "s": [{"i": [[0, 0], [85.099, -40.512], [-16.885, 56.203], [0, 0]], "o": [[0, 0], [-52.987, 25.225], [27.244, -90.685], [0, 0]], "v": [[-17.101, -120.84], [-109.398, 216.586], [-349.286, 3.974], [-62.19, -155.843]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.002, 1, 1, 1, 0.48, 1, 1, 1, 0.964, 1, 1, 1, 0, 1, 0.478, 0.5, 0.964, 0]}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [-15.256, -173.82], "to": [0, 0], "ti": [0, 0]}, {"t": 80, "s": [-66.422, -104.101]}]}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [-48.463, -128.59], "to": [0, 0], "ti": [0, 0]}, {"t": 80, "s": [-283.384, 117.82]}]}, "t": 1, "nm": "35oijsdfsd", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}], "ip": 62, "op": 81, "st": 12, "bm": 0}, {"ddd": 0, "ind": 61, "ty": 4, "nm": "woosh 4", "parent": 65, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [60]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 72, "s": [60]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 77, "s": [0]}, {"t": 78, "s": [50]}]}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 60, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 71, "s": [0]}, {"t": 82, "s": [10.538]}]}, "p": {"a": 0, "k": [490.179, 19.256, 0]}, "a": {"a": 0, "k": [-57.407, -114.607, 0]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-39.711, -84.966], [-64.264, 118.572], [-325.533, -57.029], [-86.379, -139.731]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [{"i": [[-5.967, -34.501], [0, 0], [0, 0], [-151.967, -45.71]], "o": [[16.282, 94.143], [0, 0], [0, 0], [33.53, 10.085]], "v": [[-30.041, -76.491], [-7.796, 233.345], [-459.939, -130.829], [-112.791, -145.967]], "c": true}]}, {"t": 78, "s": [{"i": [[0, 0], [85.099, -40.512], [-16.885, 56.203], [0, 0]], "o": [[0, 0], [-52.987, 25.225], [27.244, -90.685], [0, 0]], "v": [[-17.101, -120.84], [-109.398, 216.586], [-349.286, 3.974], [-62.19, -155.843]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.002, 1, 1, 1, 0.48, 1, 1, 1, 0.964, 1, 1, 1, 0, 1, 0.478, 0.5, 0.964, 0]}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [-15.256, -173.82], "to": [0, 0], "ti": [0, 0]}, {"t": 78, "s": [-66.422, -104.101]}]}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [-48.463, -128.59], "to": [0, 0], "ti": [0, 0]}, {"t": 78, "s": [-283.384, 117.82]}]}, "t": 1, "nm": "3535sf", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}], "ip": 60, "op": 79, "st": 10, "bm": 0}, {"ddd": 0, "ind": 62, "ty": 4, "nm": "woosh 3", "parent": 65, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [60]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 62, "s": [60]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 67, "s": [0]}, {"t": 68, "s": [50]}]}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 50, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 61, "s": [0]}, {"t": 72, "s": [10.538]}]}, "p": {"a": 0, "k": [221.597, 280.81, 0]}, "a": {"a": 0, "k": [-57.407, -114.607, 0]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-39.711, -84.966], [-64.264, 118.572], [-325.533, -57.029], [-86.379, -139.731]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [{"i": [[-5.967, -34.501], [0, 0], [0, 0], [-151.967, -45.71]], "o": [[16.282, 94.143], [0, 0], [0, 0], [33.53, 10.085]], "v": [[-30.041, -76.491], [-7.796, 233.345], [-459.939, -130.829], [-112.791, -145.967]], "c": true}]}, {"t": 68, "s": [{"i": [[-0.143, -23.984], [85.099, -40.512], [-16.885, 56.203], [-113.826, 14.408]], "o": [[0.637, 107.107], [-52.987, 25.225], [27.244, -90.685], [23.794, -3.012]], "v": [[-28.412, -61.265], [-177.079, 162.174], [-305.19, 41.418], [-128.761, -152.814]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.918, 1, 0, 0.478, 0.918, 1, 0, 0.964, 0.918, 1, 0, 0, 1, 0.478, 0.5, 0.964, 0]}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [-15.256, -173.82], "to": [0, 0], "ti": [0, 0]}, {"t": 68, "s": [-66.422, -104.101]}]}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [-48.463, -128.59], "to": [0, 0], "ti": [0, 0]}, {"t": 68, "s": [-283.384, 117.82]}]}, "t": 1, "nm": "3512", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}], "ip": 50, "op": 69, "st": 0, "bm": 0}, {"ddd": 0, "ind": 63, "ty": 4, "nm": "woosh 2", "parent": 65, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [60]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 62, "s": [60]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 67, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 68, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 73, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 75, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 77, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 79, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 81, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 84, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 85, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 88, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 92, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 97, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 104, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 107, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [50]}, {"t": 124, "s": [0]}]}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 50, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 61, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 72, "s": [10.538]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 90, "s": [-14.366]}, {"i": {"x": [0.12], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 115, "s": [10.538]}, {"t": 133, "s": [0]}]}, "p": {"a": 0, "k": [221.597, 280.81, 0]}, "a": {"a": 0, "k": [-57.407, -114.607, 0]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-39.711, -84.966], [-64.264, 118.572], [-325.533, -57.029], [-86.379, -139.731]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [{"i": [[-0.143, -23.984], [85.099, -40.512], [-16.885, 56.203], [-113.826, 14.408]], "o": [[0.637, 107.107], [-52.987, 25.225], [27.244, -90.685], [23.794, -3.012]], "v": [[-28.412, -61.265], [-177.079, 162.174], [-305.19, 41.418], [-128.761, -152.814]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [{"i": [[-0.143, -23.984], [85.099, -40.512], [-16.885, 56.203], [-113.826, 14.408]], "o": [[0.637, 107.107], [-52.987, 25.225], [27.244, -90.685], [23.794, -3.012]], "v": [[-28.412, -61.265], [-177.079, 162.174], [-305.19, 41.418], [-128.761, -152.814]], "c": true}]}, {"i": {"x": 0.833, "y": 0.556}, "o": {"x": 0.333, "y": 0}, "t": 123, "s": [{"i": [[0, 0], [228.742, -123.718], [-8.257, 81.679], [0, 0]], "o": [[0, 0], [-64.215, 34.732], [27.164, -268.707], [0, 0]], "v": [[-39.711, -84.966], [-177.079, 162.174], [-305.19, 41.417], [-86.379, -139.731]], "c": true}]}, {"t": 126, "s": [{"i": [[0, 0], [67.958, -148.652], [47.376, 103.042], [0, 0]], "o": [[0, 0], [-30.354, 66.397], [-47.306, -200.425], [0, 0]], "v": [[-39.711, -84.966], [-9.635, 176.414], [-221.786, 71.385], [-86.379, -139.731]], "c": true}], "h": 1}]}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.522, 1, 0, 0.478, 0.522, 1, 0, 0.964, 0.522, 1, 0, 0, 1, 0.478, 0.5, 0.964, 0]}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [-15.256, -173.82], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [-66.422, -104.101], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 115, "s": [-66.422, -104.101], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 123, "s": [-66.422, -104.101], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.649, "y": 0.901}, "o": {"x": 0.301, "y": 0}, "t": 124.125, "s": [-66.422, -104.101], "to": [0, 0], "ti": [0, 0]}, {"t": 126, "s": [-40.424, -319.285], "h": 1}]}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [-48.463, -128.59], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [-283.384, 117.82], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 115, "s": [-227.224, 98.674], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 123, "s": [-283.384, 117.82], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.649, "y": 0.828}, "o": {"x": 0.301, "y": 0}, "t": 124.125, "s": [-253.134, 88.737], "to": [0, 0], "ti": [0, 0]}, {"t": 126, "s": [-77.181, -194.436], "h": 1}]}, "t": 1, "nm": "35123", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}], "ip": 50, "op": 127, "st": 0, "bm": 0}, {"ddd": 0, "ind": 64, "ty": 4, "nm": "broom", "parent": 65, "sr": 1, "ks": {"r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 18, "s": [-18.334]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 28, "s": [13.225]}, {"i": {"x": [0.12], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 36, "s": [-18.334]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 50, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 61, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 72, "s": [10.538]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 90, "s": [-14.366]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 115, "s": [10.538]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 133, "s": [-14.366]}, {"i": {"x": [0.12], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 149, "s": [10.538]}, {"t": 179, "s": [0]}]}, "p": {"a": 0, "k": [223.962, 283.716, 0]}, "a": {"a": 0, "k": [223.962, 283.716, 0]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[-9.749, -5.599], [-24.437, -47.098], [-0.757, 18.284], [27.896, 7.555]], "o": [[0, 0], [0, 0], [0.757, -18.284], [-17.007, -4.606]], "v": [[-35.447, -22.504], [22.973, 39.372], [41.02, 30.087], [-25.379, -40.816]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [{"i": [[-9.749, -5.599], [-24.437, -47.098], [-0.757, 18.284], [27.896, 7.555]], "o": [[0, 0], [0, 0], [0.757, -18.284], [-17.007, -4.606]], "v": [[-35.447, -22.504], [22.973, 39.372], [41.02, 30.087], [-25.379, -40.816]], "c": true}]}, {"t": 22, "s": [{"i": [[-9.749, -5.599], [-24.437, -47.098], [-0.757, 18.284], [27.896, 7.555]], "o": [[0, 0], [0, 0], [0.757, -18.284], [-17.007, -4.606]], "v": [[-35.447, -22.504], [22.973, 39.372], [41.02, 30.087], [-25.379, -40.816]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 18}, "g": {"p": 3, "k": {"a": 0, "k": [0.393, 0.129, 0.137, 0.188, 0.613, 0.222, 0.208, 0.314, 0.827, 0.314, 0.278, 0.439]}}, "s": {"a": 0, "k": [1.863, 42.043]}, "e": {"a": 0, "k": [-0.282, -39.506]}, "t": 1, "lc": 2, "lj": 2, "bm": 0, "nm": "35slisdg", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.470588237047, 0.470588237047, 0.607843160629, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [216.289, 285.651]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.967], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [42.877, -46.208], [0, 0], [39.068, -34.807], [0, 0], [18.605, 0.371], [1.647, 22.492], [25.937, 39.849], [-52.58, 43.109], [-1.051, 12.216]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-8.69, 9.365], [0, 0], [-14.966, 13.334], [0, 0], [-14.193, -0.283], [0, 0], [-13.932, -21.406], [0, 0], [1.051, -12.216]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [82.958, 84.654], [70.159, 70.789], [41.265, 138.327], [21.573, 133.803], [-16.804, 162.869], [-46.925, 116.494], [-76.583, 111.879], [-74.125, -34.243], [-89.155, -43.606]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 29, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.464, -8.836], [3.098, -1.53], [60.909, -16.238], [0, 0], [47.779, 27.443], [0, 0], [6.781, 18.147], [-20.568, 8.336], [-27.618, 37.084], [-67.467, 8.443], [-7.401, 9.776]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-12.344, 3.291], [0, 0], [-18.304, -10.513], [0, 0], [-5.173, -13.843], [0, 0], [14.836, -19.921], [0, 0], [7.401, -9.776]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [1.752, 93.457], [-1.686, 74.903], [-116.22, 90.899], [-119.503, 70.454], [-161.606, 41.96], [-129.441, -1.136], [-136.457, -31.27], [-53.978, -68.993], [-61.703, -84.927]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 37, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [42.877, -46.208], [0, 0], [39.068, -34.807], [0, 0], [18.605, 0.371], [1.647, 22.492], [25.937, 39.849], [-52.58, 43.109], [-1.051, 12.216]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-8.69, 9.365], [0, 0], [-14.966, 13.334], [0, 0], [-14.193, -0.283], [0, 0], [-13.932, -21.406], [0, 0], [1.051, -12.216]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [82.958, 84.654], [70.159, 70.789], [41.265, 138.327], [21.573, 133.803], [-16.804, 162.869], [-46.925, 116.494], [-76.583, 111.879], [-74.125, -34.243], [-89.155, -43.606]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [21.628, -17.788], [0, 0], [34.074, -0.873], [0, 0], [17.578, 9.28], [3.347, 13.672], [22.979, 28.572], [-30.044, 12.974], [2.349, 7.663]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-4.383, 3.605], [0, 0], [-13.054, 0.335], [0, 0], [-13.41, -7.08], [0, 0], [-15.132, -18.815], [0, 0], [-2.349, -7.663]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [95.663, 119.155], [82.023, 106.43], [33.322, 118.619], [17.758, 94.605], [-19.493, 104.357], [-45.649, 54.425], [-89.221, 34.998], [-94.632, -45.136], [-108.853, -55.567]], "c": true}]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.33, "y": 0}, "t": 68, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [59.013, -58.294], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-11.96, 11.814], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [48.458, 83.108], [41.988, 69.721], [-39.889, 160.933], [-55.904, 153.823], [-109.898, 182.428], [-110.664, 127.326], [-136.106, 119.08], [-51.611, -24.11], [-61.996, -32.129]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [59.013, -58.294], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-11.96, 11.814], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [48.458, 83.108], [41.988, 69.721], [-39.889, 160.933], [-55.904, 153.823], [-109.898, 182.428], [-110.664, 127.326], [-136.106, 119.08], [-51.611, -24.11], [-61.996, -32.129]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [59.013, -58.294], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-11.96, 11.814], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [48.458, 83.108], [41.988, 69.721], [-39.889, 160.933], [-55.904, 153.823], [-109.898, 182.428], [-110.664, 127.326], [-136.106, 119.08], [-51.611, -24.11], [-61.996, -32.129]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [59.013, -58.294], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-11.96, 11.814], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [48.458, 83.108], [41.988, 69.721], [-39.889, 160.933], [-55.904, 153.823], [-109.898, 182.428], [-110.664, 127.326], [-136.106, 119.08], [-51.611, -24.11], [-61.996, -32.129]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [59.013, -58.294], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-11.96, 11.814], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [48.458, 83.108], [41.988, 69.721], [-39.889, 160.933], [-55.904, 153.823], [-109.898, 182.428], [-110.664, 127.326], [-136.106, 119.08], [-51.611, -24.11], [-61.996, -32.129]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [59.013, -58.294], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-11.96, 11.814], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [48.458, 83.108], [41.988, 69.721], [-39.889, 160.933], [-55.904, 153.823], [-109.898, 182.428], [-110.664, 127.326], [-136.106, 119.08], [-51.611, -24.11], [-61.996, -32.129]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 123, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 131, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [42.877, -46.208], [0, 0], [39.068, -34.807], [0, 0], [18.605, 0.371], [1.647, 22.492], [25.937, 39.849], [-52.58, 43.109], [-1.051, 12.216]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-8.69, 9.365], [0, 0], [-14.966, 13.334], [0, 0], [-14.193, -0.283], [0, 0], [-13.932, -21.406], [0, 0], [1.051, -12.216]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [82.958, 84.654], [70.159, 70.789], [41.265, 138.327], [21.573, 133.803], [-16.804, 162.869], [-46.925, 116.494], [-76.583, 111.879], [-74.125, -34.243], [-89.155, -43.606]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 151, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.464, -8.836], [3.098, -1.53], [60.909, -16.238], [0, 0], [47.779, 27.443], [0, 0], [6.781, 18.147], [-20.568, 8.336], [-36.681, 28.151], [-31.754, 9.7], [-7.401, 9.776]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-12.344, 3.291], [0, 0], [-18.304, -10.513], [0, 0], [-5.173, -13.843], [0, 0], [19.704, -15.122], [0, 0], [7.401, -9.776]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [1.752, 93.457], [-1.686, 74.903], [-116.22, 90.899], [-119.503, 70.454], [-161.606, 41.96], [-129.441, -1.136], [-126.333, -41.305], [-53.978, -68.993], [-61.703, -84.927]], "c": true}]}, {"t": 179, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 12}, "g": {"p": 3, "k": {"a": 0, "k": [0.421, 1, 1, 1, 0.633, 1, 0.991, 0.71, 1, 1, 0.982, 0.42]}}, "s": {"a": 0, "k": [75.084, -54.877]}, "e": {"a": 0, "k": [-65.506, 81.356]}, "t": 1, "lc": 2, "lj": 2, "bm": 0, "nm": "35dog", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 51}, "w": {"a": 0, "k": 23}, "g": {"p": 3, "k": {"a": 0, "k": [0.421, 1, 0.98, 0.42, 0.633, 1, 0.975, 0.21, 1, 1, 0.969, 0]}}, "s": {"a": 0, "k": [75.084, -54.877]}, "e": {"a": 0, "k": [-65.506, 81.356]}, "t": 1, "lc": 2, "lj": 2, "bm": 0, "nm": "35cat", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 51}, "w": {"a": 0, "k": 32}, "g": {"p": 3, "k": {"a": 0, "k": [0.421, 1, 0.98, 0.42, 0.633, 1, 0.975, 0.21, 1, 1, 0.969, 0]}}, "s": {"a": 0, "k": [75.084, -54.877]}, "e": {"a": 0, "k": [-65.506, 81.356]}, "t": 1, "lc": 2, "lj": 2, "bm": 0, "nm": "35mouse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.995807124119, 0.866666666667, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [150.962, 343.716]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 42, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 64, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 70, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 73, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 74, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 78, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 79, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 83, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 84, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 88, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 93, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 94, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 98, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 99, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 103, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 104, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 108, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 109, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 113, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 114, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 119, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 123, "s": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 124, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 131, "s": [100]}, {"t": 150, "s": [0]}]}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "glint", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.967], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [42.877, -46.208], [0, 0], [39.068, -34.807], [0, 0], [18.605, 0.371], [1.647, 22.492], [25.937, 39.849], [-52.58, 43.109], [-1.051, 12.216]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-8.69, 9.365], [0, 0], [-14.966, 13.334], [0, 0], [-14.193, -0.283], [0, 0], [-13.932, -21.406], [0, 0], [1.051, -12.216]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [82.958, 84.654], [70.159, 70.789], [41.265, 138.327], [21.573, 133.803], [-16.804, 162.869], [-46.925, 116.494], [-76.583, 111.879], [-74.125, -34.243], [-89.155, -43.606]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 29, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.464, -8.836], [3.098, -1.53], [60.909, -16.238], [0, 0], [47.779, 27.443], [0, 0], [6.781, 18.147], [-20.568, 8.336], [-27.618, 37.084], [-67.467, 8.443], [-7.401, 9.776]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-12.344, 3.291], [0, 0], [-18.304, -10.513], [0, 0], [-5.173, -13.843], [0, 0], [14.836, -19.921], [0, 0], [7.401, -9.776]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [1.752, 93.457], [-1.686, 74.903], [-116.22, 90.899], [-119.503, 70.454], [-161.606, 41.96], [-129.441, -1.136], [-136.457, -31.27], [-53.978, -68.993], [-61.703, -84.927]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 37, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [42.877, -46.208], [0, 0], [39.068, -34.807], [0, 0], [18.605, 0.371], [1.647, 22.492], [25.937, 39.849], [-52.58, 43.109], [-1.051, 12.216]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-8.69, 9.365], [0, 0], [-14.966, 13.334], [0, 0], [-14.193, -0.283], [0, 0], [-13.932, -21.406], [0, 0], [1.051, -12.216]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [82.958, 84.654], [70.159, 70.789], [41.265, 138.327], [21.573, 133.803], [-16.804, 162.869], [-46.925, 116.494], [-76.583, 111.879], [-74.125, -34.243], [-89.155, -43.606]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [21.628, -17.788], [0, 0], [34.074, -0.873], [0, 0], [17.578, 9.28], [3.347, 13.672], [22.979, 28.572], [-30.044, 12.974], [2.349, 7.663]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-4.383, 3.605], [0, 0], [-13.054, 0.335], [0, 0], [-13.41, -7.08], [0, 0], [-15.132, -18.815], [0, 0], [-2.349, -7.663]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [95.663, 119.155], [82.023, 106.43], [33.322, 118.619], [17.758, 94.605], [-19.493, 104.357], [-45.649, 54.425], [-89.221, 34.998], [-94.632, -45.136], [-108.853, -55.567]], "c": true}]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.33, "y": 0}, "t": 68, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [59.013, -58.294], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-11.96, 11.814], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [48.458, 83.108], [41.988, 69.721], [-39.889, 160.933], [-55.904, 153.823], [-109.898, 182.428], [-110.664, 127.326], [-136.106, 119.08], [-51.611, -24.11], [-61.996, -32.129]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [59.013, -58.294], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-11.96, 11.814], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [48.458, 83.108], [41.988, 69.721], [-39.889, 160.933], [-55.904, 153.823], [-109.898, 182.428], [-110.664, 127.326], [-136.106, 119.08], [-51.611, -24.11], [-61.996, -32.129]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [59.013, -58.294], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-11.96, 11.814], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [48.458, 83.108], [41.988, 69.721], [-39.889, 160.933], [-55.904, 153.823], [-109.898, 182.428], [-110.664, 127.326], [-136.106, 119.08], [-51.611, -24.11], [-61.996, -32.129]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [59.013, -58.294], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-11.96, 11.814], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [48.458, 83.108], [41.988, 69.721], [-39.889, 160.933], [-55.904, 153.823], [-109.898, 182.428], [-110.664, 127.326], [-136.106, 119.08], [-51.611, -24.11], [-61.996, -32.129]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [59.013, -58.294], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-11.96, 11.814], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [48.458, 83.108], [41.988, 69.721], [-39.889, 160.933], [-55.904, 153.823], [-109.898, 182.428], [-110.664, 127.326], [-136.106, 119.08], [-51.611, -24.11], [-61.996, -32.129]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [59.013, -58.294], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-11.96, 11.814], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [48.458, 83.108], [41.988, 69.721], [-39.889, 160.933], [-55.904, 153.823], [-109.898, 182.428], [-110.664, 127.326], [-136.106, 119.08], [-51.611, -24.11], [-61.996, -32.129]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 123, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 131, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [42.877, -46.208], [0, 0], [39.068, -34.807], [0, 0], [18.605, 0.371], [1.647, 22.492], [25.937, 39.849], [-52.58, 43.109], [-1.051, 12.216]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-8.69, 9.365], [0, 0], [-14.966, 13.334], [0, 0], [-14.193, -0.283], [0, 0], [-13.932, -21.406], [0, 0], [1.051, -12.216]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [82.958, 84.654], [70.159, 70.789], [41.265, 138.327], [21.573, 133.803], [-16.804, 162.869], [-46.925, 116.494], [-76.583, 111.879], [-74.125, -34.243], [-89.155, -43.606]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 151, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.464, -8.836], [3.098, -1.53], [60.909, -16.238], [0, 0], [47.779, 27.443], [0, 0], [6.781, 18.147], [-20.568, 8.336], [-36.681, 28.151], [-31.754, 9.7], [-7.401, 9.776]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-12.344, 3.291], [0, 0], [-18.304, -10.513], [0, 0], [-5.173, -13.843], [0, 0], [19.704, -15.122], [0, 0], [7.401, -9.776]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [1.752, 93.457], [-1.686, 74.903], [-116.22, 90.899], [-119.503, 70.454], [-161.606, 41.96], [-129.441, -1.136], [-126.333, -41.305], [-53.978, -68.993], [-61.703, -84.927]], "c": true}]}, {"t": 179, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 18}, "g": {"p": 3, "k": {"a": 0, "k": [0.421, 0.365, 0.153, 0.129, 0.633, 0.433, 0.22, 0.145, 1, 0.502, 0.286, 0.161]}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.66, "y": 0}, "t": 0, "s": [2.755, 140.651], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [142.976, -131.717], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 29, "s": [-116.67, -61.595], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 132, "s": [-116.67, -61.595], "to": [0, 0], "ti": [0, 0]}, {"t": 149, "s": [2.755, 140.651]}]}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.66, "y": 0}, "t": 0, "s": [1.962, -143.851], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [-138.162, 97.679], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 29, "s": [40.427, 79.821], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 132, "s": [40.427, 79.821], "to": [0, 0], "ti": [0, 0]}, {"t": 149, "s": [1.962, -143.851]}]}, "t": 1, "lc": 2, "lj": 2, "bm": 0, "nm": "35ligma", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [150.962, 343.716]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [24.176, -28.899]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-9.061, 24.437]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [{"i": [[0, 0], [-5.53, -36.561]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-2.297, 42.398]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [{"i": [[0, 0], [24.176, -28.899]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-9.061, 24.437]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 29, "s": [{"i": [[0, 0], [30.376, -16.421]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-32.486, 15.682]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [{"i": [[0, 0], [24.176, -28.899]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-9.061, 24.437]], "c": false}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 37, "s": [{"i": [[0, 0], [-5.53, -36.561]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-2.297, 42.398]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [{"i": [[0, 0], [13.322, -31.332]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [2.986, 25.196]], "c": false}]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [{"i": [[0, 0], [24.176, -28.899]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-9.061, 24.437]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.33, "y": 0}, "t": 68, "s": [{"i": [[0, 0], [24.176, -28.899]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-38.644, 53.925]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [{"i": [[0, 0], [24.176, -28.899]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-9.061, 24.437]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [{"i": [[0, 0], [24.176, -28.899]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-38.644, 53.925]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [{"i": [[0, 0], [24.176, -28.899]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-9.061, 24.437]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [{"i": [[0, 0], [24.176, -28.899]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-38.644, 53.925]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [{"i": [[0, 0], [24.176, -28.899]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-9.061, 24.437]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [{"i": [[0, 0], [24.176, -28.899]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-38.644, 53.925]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103, "s": [{"i": [[0, 0], [24.176, -28.899]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-9.061, 24.437]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [{"i": [[0, 0], [24.176, -28.899]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-38.644, 53.925]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [{"i": [[0, 0], [24.176, -28.899]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-9.061, 24.437]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118, "s": [{"i": [[0, 0], [24.176, -28.899]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-38.644, 53.925]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 123, "s": [{"i": [[0, 0], [24.176, -28.899]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-9.061, 24.437]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 131, "s": [{"i": [[0, 0], [-5.53, -36.561]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-2.297, 42.398]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [{"i": [[0, 0], [24.176, -28.899]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-9.061, 24.437]], "c": false}]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 151, "s": [{"i": [[0, 0], [30.376, -16.421]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-32.486, 15.682]], "c": false}]}, {"t": 179, "s": [{"i": [[0, 0], [24.176, -28.899]], "o": [[0, 0], [0, 0]], "v": [[8.121, -24.437], [-9.061, 24.437]], "c": false}]}]}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.737254917622, 0.439215689898, 0.168627455831, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 18}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [212.317, 309.938]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [-45.937, -5.027]], "o": [[0, 0], [0, 0]], "v": [[-36.239, 11.581], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [{"i": [[0, 0], [-46.526, 5.812]], "o": [[0, 0], [0, 0]], "v": [[-15.116, 36.704], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [{"i": [[0, 0], [-45.937, -5.027]], "o": [[0, 0], [0, 0]], "v": [[-36.239, 11.581], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 29, "s": [{"i": [[0, 0], [-47.472, 1.1]], "o": [[0, 0], [0, 0]], "v": [[-42.71, -7.769], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [{"i": [[0, 0], [-45.937, -5.027]], "o": [[0, 0], [0, 0]], "v": [[-36.239, 11.581], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 37, "s": [{"i": [[0, 0], [-46.526, 5.812]], "o": [[0, 0], [0, 0]], "v": [[-15.116, 36.704], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [{"i": [[0, 0], [-29.92, -13.645]], "o": [[0, 0], [0, 0]], "v": [[-27.448, -11.658], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [{"i": [[0, 0], [-45.937, -5.027]], "o": [[0, 0], [0, 0]], "v": [[-36.239, 11.581], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.33, "y": 0}, "t": 68, "s": [{"i": [[0, 0], [-45.937, -5.027]], "o": [[0, 0], [0, 0]], "v": [[-48.115, 44.014], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [{"i": [[0, 0], [-45.937, -5.027]], "o": [[0, 0], [0, 0]], "v": [[-36.239, 11.581], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [{"i": [[0, 0], [-45.937, -5.027]], "o": [[0, 0], [0, 0]], "v": [[-48.115, 44.014], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [{"i": [[0, 0], [-45.937, -5.027]], "o": [[0, 0], [0, 0]], "v": [[-36.239, 11.581], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [{"i": [[0, 0], [-45.937, -5.027]], "o": [[0, 0], [0, 0]], "v": [[-48.115, 44.014], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [{"i": [[0, 0], [-45.937, -5.027]], "o": [[0, 0], [0, 0]], "v": [[-36.239, 11.581], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [{"i": [[0, 0], [-45.937, -5.027]], "o": [[0, 0], [0, 0]], "v": [[-48.115, 44.014], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103, "s": [{"i": [[0, 0], [-45.937, -5.027]], "o": [[0, 0], [0, 0]], "v": [[-36.239, 11.581], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [{"i": [[0, 0], [-45.937, -5.027]], "o": [[0, 0], [0, 0]], "v": [[-48.115, 44.014], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [{"i": [[0, 0], [-45.937, -5.027]], "o": [[0, 0], [0, 0]], "v": [[-36.239, 11.581], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118, "s": [{"i": [[0, 0], [-45.937, -5.027]], "o": [[0, 0], [0, 0]], "v": [[-48.115, 44.014], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 123, "s": [{"i": [[0, 0], [-45.937, -5.027]], "o": [[0, 0], [0, 0]], "v": [[-36.239, 11.581], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 131, "s": [{"i": [[0, 0], [-46.526, 5.812]], "o": [[0, 0], [0, 0]], "v": [[-15.116, 36.704], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [{"i": [[0, 0], [-45.937, -5.027]], "o": [[0, 0], [0, 0]], "v": [[-36.239, 11.581], [36.239, -10.969]], "c": false}]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 151, "s": [{"i": [[0, 0], [-47.472, 1.1]], "o": [[0, 0], [0, 0]], "v": [[-42.71, -7.769], [36.239, -10.969]], "c": false}]}, {"t": 179, "s": [{"i": [[0, 0], [-45.937, -5.027]], "o": [[0, 0], [0, 0]], "v": [[-36.239, 11.581], [36.239, -10.969]], "c": false}]}]}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.737254917622, 0.439215689898, 0.168627455831, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 18}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [175.862, 296.049]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 7", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[37.207, -24.362], [0, 0], [55.675, -10.959], [0, 0], [18.895, 9.924], [-8.421, 20.939], [8.659, 48.961], [-62.574, 30.136], [-23.517, -6.491], [0, 0], [-23.163, -10.745], [-0.948, 0.861], [-21.405, 3.736], [-0.661, 1.095], [-11.805, -1.826], [-2.832, 32.382]], "o": [[-10.681, 7.001], [0, 0], [-21.318, 4.202], [0, 0], [-14.419, -7.572], [0, 0], [-4.174, -23.541], [-28.56, 27.081], [17.492, 4.828], [0, 0], [13.073, 6.064], [-0.542, 0.866], [25.971, -4.533], [0, 0], [13.51, 2.09], [1.513, 26.993]], "v": [[75.729, 55.289], [66.645, 38.752], [-4.799, 97.283], [-22.936, 83.097], [-75.32, 89.276], [-85.336, 32.323], [-113.652, 12.916], [-48.13, -97.907], [-90.77, -18.059], [-32.529, -45.39], [-44.767, 23.906], [4.983, -6.534], [6.889, 42.507], [76.275, -33.064], [84.3, 8.928], [114.318, -45.47]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [{"i": [[32.618, -30.231], [0, 0], [35.307, -30.421], [0, 0], [16.876, 3.708], [2.684, 23.993], [26.565, 40.024], [-56.684, 40.132], [-14.334, 1.133], [0, 0], [-24.629, -6.739], [-0.792, 1.007], [-20.485, 7.248], [-0.469, 1.19], [-11.944, 0.164], [2.598, 32.401]], "o": [[-9.366, 8.681], [0, 0], [-16.461, 14.183], [0, 0], [-15.907, -3.495], [0, 0], [-13.222, -19.92], [-23.654, 31.457], [18.09, -1.43], [0, 0], [13.9, 3.804], [-0.39, 0.944], [24.854, -8.793], [0, 0], [13.67, -0.188], [5.986, 26.364]], "v": [[105.884, 40.135], [94.173, 25.341], [65.205, 92.527], [45.755, 88.084], [6.65, 117.389], [-22.814, 70.873], [-52.884, 66.313], [-49.269, -79.876], [-38.354, 27.134], [-5.605, -20.545], [1.435, 46.731], [30.01, 6.458], [56.984, 45.21], [84.194, -36.647], [99.097, 3.423], [119.403, -50.247]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [{"i": [[37.207, -24.362], [0, 0], [55.675, -10.959], [0, 0], [18.895, 9.924], [-8.421, 20.939], [8.659, 48.961], [-62.574, 30.136], [-23.517, -6.491], [0, 0], [-23.163, -10.745], [-0.948, 0.861], [-21.405, 3.736], [-0.661, 1.095], [-11.805, -1.826], [-2.832, 32.382]], "o": [[-10.681, 7.001], [0, 0], [-21.318, 4.202], [0, 0], [-14.419, -7.572], [0, 0], [-4.174, -23.541], [-28.56, 27.081], [17.492, 4.828], [0, 0], [13.073, 6.064], [-0.542, 0.866], [25.971, -4.533], [0, 0], [13.51, 2.09], [1.513, 26.993]], "v": [[75.729, 55.289], [66.645, 38.752], [-4.799, 97.283], [-22.936, 83.097], [-75.32, 89.276], [-85.336, 32.323], [-113.652, 12.916], [-48.13, -97.907], [-90.77, -18.059], [-32.529, -45.39], [-44.767, 23.906], [4.983, -6.534], [6.889, 42.507], [76.275, -33.064], [84.3, 8.928], [114.318, -45.47]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 29, "s": [{"i": [[46.032, -12.758], [0, 0], [49.827, 27.148], [0, 0], [7.591, 19.947], [-20.349, 7.625], [-27.27, 36.077], [-67.374, 7.566], [-21.843, -10.866], [0, 0], [-20.682, -14.974], [-1.095, 0.664], [-21.725, -0.424], [-0.858, 0.949], [-11.238, -4.049], [-16.972, 29.322]], "o": [[-12.307, 3.411], [0, 0], [-17.798, -9.697], [0, 0], [-5.551, -14.587], [0, 0], [16.14, -21.352], [-33.21, 21.123], [16.247, 8.082], [0, 0], [11.673, 8.451], [-0.697, 0.746], [26.359, 0.514], [0, 0], [12.862, 4.634], [-3.674, 26.784]], "v": [[25.667, 48.005], [22.197, 29.769], [-92.077, 46.149], [-96.017, 24.921], [-136.658, -3.113], [-105.429, -45.965], [-112.404, -75.725], [-30.016, -113.558], [-84.944, -63.939], [-39.145, -71.912], [-83.869, -16.137], [-12.414, -29.581], [-34.746, 8.786], [46.23, -40.52], [49.217, -3.252], [105.01, -43.924]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [{"i": [[37.207, -24.362], [0, 0], [55.675, -10.959], [0, 0], [18.895, 9.924], [-8.421, 20.939], [8.659, 48.961], [-62.574, 30.136], [-23.517, -6.491], [0, 0], [-23.163, -10.745], [-0.948, 0.861], [-21.405, 3.736], [-0.661, 1.095], [-11.805, -1.826], [-2.832, 32.382]], "o": [[-10.681, 7.001], [0, 0], [-21.318, 4.202], [0, 0], [-14.419, -7.572], [0, 0], [-4.174, -23.541], [-28.56, 27.081], [17.492, 4.828], [0, 0], [13.073, 6.064], [-0.542, 0.866], [25.971, -4.533], [0, 0], [13.51, 2.09], [1.513, 26.993]], "v": [[75.729, 55.289], [66.645, 38.752], [-4.799, 97.283], [-22.936, 83.097], [-75.32, 89.276], [-85.336, 32.323], [-113.652, 12.916], [-48.13, -97.907], [-90.77, -18.059], [-32.529, -45.39], [-44.767, 23.906], [4.983, -6.534], [6.889, 42.507], [76.275, -33.064], [84.3, 8.928], [114.318, -45.47]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 37, "s": [{"i": [[32.618, -30.231], [0, 0], [35.307, -30.421], [0, 0], [16.876, 3.708], [2.684, 23.993], [26.565, 40.024], [-56.684, 40.132], [-14.334, 1.133], [0, 0], [-24.629, -6.739], [-0.792, 1.007], [-20.485, 7.248], [-0.469, 1.19], [-11.944, 0.164], [2.598, 32.401]], "o": [[-9.366, 8.681], [0, 0], [-16.461, 14.183], [0, 0], [-15.907, -3.495], [0, 0], [-13.222, -19.92], [-23.654, 31.457], [18.09, -1.43], [0, 0], [13.9, 3.804], [-0.39, 0.944], [24.854, -8.793], [0, 0], [13.67, -0.188], [5.986, 26.364]], "v": [[105.884, 40.135], [94.173, 25.341], [65.205, 92.527], [45.755, 88.084], [6.65, 117.389], [-22.814, 70.873], [-52.884, 66.313], [-49.269, -79.876], [-38.354, 27.134], [-5.605, -20.545], [1.435, 46.731], [30.01, 6.458], [56.984, 45.21], [84.194, -36.647], [99.097, 3.423], [119.403, -50.247]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [{"i": [[37.881, -23.3], [0, 0], [34.712, 1.893], [0, 0], [15.267, 8.091], [5.248, 23.301], [26.15, 31.433], [-62.122, 12.84], [-14.334, 1.134], [0, 0], [-24.629, -6.739], [-0.792, 1.007], [-20.485, 7.248], [-0.469, 1.19], [-11.944, 0.164], [2.598, 32.401]], "o": [[-5.648, 3.474], [0, 0], [-13.53, -0.738], [0, 0], [-12.673, -6.716], [0, 0], [-15.291, -18.38], [-23.654, 31.457], [18.09, -1.43], [0, 0], [13.9, 3.804], [-0.39, 0.944], [24.854, -8.793], [0, 0], [13.67, -0.188], [5.986, 26.364]], "v": [[119.8, 73.639], [105.955, 61.103], [57.779, 73.107], [42.188, 48.683], [4.467, 59.843], [-21.234, 8.917], [-64.439, -12.712], [-49.843, -99.666], [-55.329, -45.981], [-2.172, -77.543], [-12.122, -20.026], [31.994, -42.995], [26.82, 19.625], [74.931, -17.505], [80.996, 22.118], [112.801, -51.41]], "c": true}]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [{"i": [[37.207, -24.362], [0, 0], [55.675, -10.959], [0, 0], [18.895, 9.924], [-8.421, 20.939], [8.659, 48.961], [-62.574, 30.136], [-23.517, -6.491], [0, 0], [-23.163, -10.745], [-0.948, 0.861], [-21.405, 3.736], [-0.661, 1.095], [-11.805, -1.826], [-2.832, 32.382]], "o": [[-10.681, 7.001], [0, 0], [-21.318, 4.202], [0, 0], [-14.419, -7.572], [0, 0], [-4.174, -23.541], [-28.56, 27.081], [17.492, 4.828], [0, 0], [13.073, 6.064], [-0.542, 0.866], [25.971, -4.533], [0, 0], [13.51, 2.09], [1.513, 26.993]], "v": [[75.729, 55.289], [66.645, 38.752], [-4.799, 97.283], [-22.936, 83.097], [-75.32, 89.276], [-85.336, 32.323], [-113.652, 12.916], [-48.13, -97.907], [-90.77, -18.059], [-32.529, -45.39], [-44.767, 23.906], [4.983, -6.534], [6.889, 42.507], [76.275, -33.064], [84.3, 8.928], [114.318, -45.47]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.33, "y": 0}, "t": 68, "s": [{"i": [[14.158, -14.141], [0, 0], [63.114, -32.685], [0, 0], [17.087, 6.077], [-15.67, 29.199], [0.29, 46.183], [-65.184, 54.629], [-23.127, -0.191], [0, 0], [-21.397, -5.664], [-1.289, 1.405], [-24.113, 11.959], [-1.056, 1.602], [-11.033, -5.168], [-13.318, 41.7]], "o": [[-13.069, 13.053], [0, 0], [-24.168, 12.516], [0, 0], [-13.039, -4.637], [0, 0], [-0.178, -28.361], [-39.191, 43.755], [17.202, 0.142], [0, 0], [12.076, 3.197], [-0.856, 1.273], [29.256, -14.51], [0, 0], [3.699, 1.733], [-6.952, 33.446]], "v": [[72.485, 38.528], [65.909, 24.261], [-15.928, 115.691], [-32.02, 108.901], [-85.741, 137.372], [-86.77, 82.358], [-111.841, 73.795], [-27.601, -67.855], [-57.64, 21.668], [-22.685, 0.919], [-45.763, 63.481], [14.421, 5.746], [-5.698, 63.986], [63.362, -19.445], [77.879, -4.665], [112.69, -32.423]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [{"i": [[37.207, -24.362], [0, 0], [55.675, -10.959], [0, 0], [18.895, 9.924], [-8.421, 20.939], [8.659, 48.961], [-62.574, 30.136], [-23.517, -6.491], [0, 0], [-23.163, -10.745], [-0.948, 0.861], [-21.405, 3.736], [-0.661, 1.095], [-11.805, -1.826], [-2.832, 32.382]], "o": [[-10.681, 7.001], [0, 0], [-21.318, 4.202], [0, 0], [-14.419, -7.572], [0, 0], [-4.174, -23.541], [-28.56, 27.081], [17.492, 4.828], [0, 0], [13.073, 6.064], [-0.542, 0.866], [25.971, -4.533], [0, 0], [13.51, 2.09], [1.513, 26.993]], "v": [[75.729, 55.289], [66.645, 38.752], [-4.799, 97.283], [-22.936, 83.097], [-75.32, 89.276], [-85.336, 32.323], [-113.652, 12.916], [-48.13, -97.907], [-90.77, -18.059], [-32.529, -45.39], [-44.767, 23.906], [4.983, -6.534], [6.889, 42.507], [76.275, -33.064], [84.3, 8.928], [114.318, -45.47]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [{"i": [[14.158, -14.141], [0, 0], [63.114, -32.685], [0, 0], [17.087, 6.077], [-15.67, 29.199], [0.29, 46.183], [-65.184, 54.629], [-23.127, -0.191], [0, 0], [-21.397, -5.664], [-1.289, 1.405], [-24.113, 11.959], [-1.056, 1.602], [-11.033, -5.168], [-13.318, 41.7]], "o": [[-13.069, 13.053], [0, 0], [-24.168, 12.516], [0, 0], [-13.039, -4.637], [0, 0], [-0.178, -28.361], [-39.191, 43.755], [17.202, 0.142], [0, 0], [12.076, 3.197], [-0.856, 1.273], [29.256, -14.51], [0, 0], [3.699, 1.733], [-6.952, 33.446]], "v": [[72.485, 38.528], [65.909, 24.261], [-15.928, 115.691], [-32.02, 108.901], [-85.741, 137.372], [-86.77, 82.358], [-111.841, 73.795], [-27.601, -67.855], [-57.64, 21.668], [-22.685, 0.919], [-45.763, 63.481], [14.421, 5.746], [-5.698, 63.986], [63.362, -19.445], [77.879, -4.665], [112.69, -32.423]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [{"i": [[37.207, -24.362], [0, 0], [55.675, -10.959], [0, 0], [18.895, 9.924], [-8.421, 20.939], [8.659, 48.961], [-62.574, 30.136], [-23.517, -6.491], [0, 0], [-23.163, -10.745], [-0.948, 0.861], [-21.405, 3.736], [-0.661, 1.095], [-11.805, -1.826], [-2.832, 32.382]], "o": [[-10.681, 7.001], [0, 0], [-21.318, 4.202], [0, 0], [-14.419, -7.572], [0, 0], [-4.174, -23.541], [-28.56, 27.081], [17.492, 4.828], [0, 0], [13.073, 6.064], [-0.542, 0.866], [25.971, -4.533], [0, 0], [13.51, 2.09], [1.513, 26.993]], "v": [[75.729, 55.289], [66.645, 38.752], [-4.799, 97.283], [-22.936, 83.097], [-75.32, 89.276], [-85.336, 32.323], [-113.652, 12.916], [-48.13, -97.907], [-90.77, -18.059], [-32.529, -45.39], [-44.767, 23.906], [4.983, -6.534], [6.889, 42.507], [76.275, -33.064], [84.3, 8.928], [114.318, -45.47]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [{"i": [[14.158, -14.141], [0, 0], [63.114, -32.685], [0, 0], [17.087, 6.077], [-15.67, 29.199], [0.29, 46.183], [-65.184, 54.629], [-23.127, -0.191], [0, 0], [-21.397, -5.664], [-1.289, 1.405], [-24.113, 11.959], [-1.056, 1.602], [-11.033, -5.168], [-13.318, 41.7]], "o": [[-13.069, 13.053], [0, 0], [-24.168, 12.516], [0, 0], [-13.039, -4.637], [0, 0], [-0.178, -28.361], [-39.191, 43.755], [17.202, 0.142], [0, 0], [12.076, 3.197], [-0.856, 1.273], [29.256, -14.51], [0, 0], [3.699, 1.733], [-6.952, 33.446]], "v": [[72.485, 38.528], [65.909, 24.261], [-15.928, 115.691], [-32.02, 108.901], [-85.741, 137.372], [-86.77, 82.358], [-111.841, 73.795], [-27.601, -67.855], [-57.64, 21.668], [-22.685, 0.919], [-45.763, 63.481], [14.421, 5.746], [-5.698, 63.986], [63.362, -19.445], [77.879, -4.665], [112.69, -32.423]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [{"i": [[37.207, -24.362], [0, 0], [55.675, -10.959], [0, 0], [18.895, 9.924], [-8.421, 20.939], [8.659, 48.961], [-62.574, 30.136], [-23.517, -6.491], [0, 0], [-23.163, -10.745], [-0.948, 0.861], [-21.405, 3.736], [-0.661, 1.095], [-11.805, -1.826], [-2.832, 32.382]], "o": [[-10.681, 7.001], [0, 0], [-21.318, 4.202], [0, 0], [-14.419, -7.572], [0, 0], [-4.174, -23.541], [-28.56, 27.081], [17.492, 4.828], [0, 0], [13.073, 6.064], [-0.542, 0.866], [25.971, -4.533], [0, 0], [13.51, 2.09], [1.513, 26.993]], "v": [[75.729, 55.289], [66.645, 38.752], [-4.799, 97.283], [-22.936, 83.097], [-75.32, 89.276], [-85.336, 32.323], [-113.652, 12.916], [-48.13, -97.907], [-90.77, -18.059], [-32.529, -45.39], [-44.767, 23.906], [4.983, -6.534], [6.889, 42.507], [76.275, -33.064], [84.3, 8.928], [114.318, -45.47]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [{"i": [[14.158, -14.141], [0, 0], [63.114, -32.685], [0, 0], [17.087, 6.077], [-15.67, 29.199], [0.29, 46.183], [-65.184, 54.629], [-23.127, -0.191], [0, 0], [-21.397, -5.664], [-1.289, 1.405], [-24.113, 11.959], [-1.056, 1.602], [-11.033, -5.168], [-13.318, 41.7]], "o": [[-13.069, 13.053], [0, 0], [-24.168, 12.516], [0, 0], [-13.039, -4.637], [0, 0], [-0.178, -28.361], [-39.191, 43.755], [17.202, 0.142], [0, 0], [12.076, 3.197], [-0.856, 1.273], [29.256, -14.51], [0, 0], [3.699, 1.733], [-6.952, 33.446]], "v": [[72.485, 38.528], [65.909, 24.261], [-15.928, 115.691], [-32.02, 108.901], [-85.741, 137.372], [-86.77, 82.358], [-111.841, 73.795], [-27.601, -67.855], [-57.64, 21.668], [-22.685, 0.919], [-45.763, 63.481], [14.421, 5.746], [-5.698, 63.986], [63.362, -19.445], [77.879, -4.665], [112.69, -32.423]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103, "s": [{"i": [[37.207, -24.362], [0, 0], [55.675, -10.959], [0, 0], [18.895, 9.924], [-8.421, 20.939], [8.659, 48.961], [-62.574, 30.136], [-23.517, -6.491], [0, 0], [-23.163, -10.745], [-0.948, 0.861], [-21.405, 3.736], [-0.661, 1.095], [-11.805, -1.826], [-2.832, 32.382]], "o": [[-10.681, 7.001], [0, 0], [-21.318, 4.202], [0, 0], [-14.419, -7.572], [0, 0], [-4.174, -23.541], [-28.56, 27.081], [17.492, 4.828], [0, 0], [13.073, 6.064], [-0.542, 0.866], [25.971, -4.533], [0, 0], [13.51, 2.09], [1.513, 26.993]], "v": [[75.729, 55.289], [66.645, 38.752], [-4.799, 97.283], [-22.936, 83.097], [-75.32, 89.276], [-85.336, 32.323], [-113.652, 12.916], [-48.13, -97.907], [-90.77, -18.059], [-32.529, -45.39], [-44.767, 23.906], [4.983, -6.534], [6.889, 42.507], [76.275, -33.064], [84.3, 8.928], [114.318, -45.47]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [{"i": [[14.158, -14.141], [0, 0], [63.114, -32.685], [0, 0], [17.087, 6.077], [-15.67, 29.199], [0.29, 46.183], [-65.184, 54.629], [-23.127, -0.191], [0, 0], [-21.397, -5.664], [-1.289, 1.405], [-24.113, 11.959], [-1.056, 1.602], [-11.033, -5.168], [-13.318, 41.7]], "o": [[-13.069, 13.053], [0, 0], [-24.168, 12.516], [0, 0], [-13.039, -4.637], [0, 0], [-0.178, -28.361], [-39.191, 43.755], [17.202, 0.142], [0, 0], [12.076, 3.197], [-0.856, 1.273], [29.256, -14.51], [0, 0], [3.699, 1.733], [-6.952, 33.446]], "v": [[72.485, 38.528], [65.909, 24.261], [-15.928, 115.691], [-32.02, 108.901], [-85.741, 137.372], [-86.77, 82.358], [-111.841, 73.795], [-27.601, -67.855], [-57.64, 21.668], [-22.685, 0.919], [-45.763, 63.481], [14.421, 5.746], [-5.698, 63.986], [63.362, -19.445], [77.879, -4.665], [112.69, -32.423]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [{"i": [[37.207, -24.362], [0, 0], [55.675, -10.959], [0, 0], [18.895, 9.924], [-8.421, 20.939], [8.659, 48.961], [-62.574, 30.136], [-23.517, -6.491], [0, 0], [-23.163, -10.745], [-0.948, 0.861], [-21.405, 3.736], [-0.661, 1.095], [-11.805, -1.826], [-2.832, 32.382]], "o": [[-10.681, 7.001], [0, 0], [-21.318, 4.202], [0, 0], [-14.419, -7.572], [0, 0], [-4.174, -23.541], [-28.56, 27.081], [17.492, 4.828], [0, 0], [13.073, 6.064], [-0.542, 0.866], [25.971, -4.533], [0, 0], [13.51, 2.09], [1.513, 26.993]], "v": [[75.729, 55.289], [66.645, 38.752], [-4.799, 97.283], [-22.936, 83.097], [-75.32, 89.276], [-85.336, 32.323], [-113.652, 12.916], [-48.13, -97.907], [-90.77, -18.059], [-32.529, -45.39], [-44.767, 23.906], [4.983, -6.534], [6.889, 42.507], [76.275, -33.064], [84.3, 8.928], [114.318, -45.47]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118, "s": [{"i": [[14.158, -14.141], [0, 0], [63.114, -32.685], [0, 0], [17.087, 6.077], [-15.67, 29.199], [0.29, 46.183], [-65.184, 54.629], [-23.127, -0.191], [0, 0], [-21.397, -5.664], [-1.289, 1.405], [-24.113, 11.959], [-1.056, 1.602], [-11.033, -5.168], [-13.318, 41.7]], "o": [[-13.069, 13.053], [0, 0], [-24.168, 12.516], [0, 0], [-13.039, -4.637], [0, 0], [-0.178, -28.361], [-39.191, 43.755], [17.202, 0.142], [0, 0], [12.076, 3.197], [-0.856, 1.273], [29.256, -14.51], [0, 0], [3.699, 1.733], [-6.952, 33.446]], "v": [[72.485, 38.528], [65.909, 24.261], [-15.928, 115.691], [-32.02, 108.901], [-85.741, 137.372], [-86.77, 82.358], [-111.841, 73.795], [-27.601, -67.855], [-57.64, 21.668], [-22.685, 0.919], [-45.763, 63.481], [14.421, 5.746], [-5.698, 63.986], [63.362, -19.445], [77.879, -4.665], [112.69, -32.423]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 123, "s": [{"i": [[37.207, -24.362], [0, 0], [55.675, -10.959], [0, 0], [18.895, 9.924], [-8.421, 20.939], [8.659, 48.961], [-62.574, 30.136], [-23.517, -6.491], [0, 0], [-23.163, -10.745], [-0.948, 0.861], [-21.405, 3.736], [-0.661, 1.095], [-11.805, -1.826], [-2.832, 32.382]], "o": [[-10.681, 7.001], [0, 0], [-21.318, 4.202], [0, 0], [-14.419, -7.572], [0, 0], [-4.174, -23.541], [-28.56, 27.081], [17.492, 4.828], [0, 0], [13.073, 6.064], [-0.542, 0.866], [25.971, -4.533], [0, 0], [13.51, 2.09], [1.513, 26.993]], "v": [[75.729, 55.289], [66.645, 38.752], [-4.799, 97.283], [-22.936, 83.097], [-75.32, 89.276], [-85.336, 32.323], [-113.652, 12.916], [-48.13, -97.907], [-90.77, -18.059], [-32.529, -45.39], [-44.767, 23.906], [4.983, -6.534], [6.889, 42.507], [76.275, -33.064], [84.3, 8.928], [114.318, -45.47]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 131, "s": [{"i": [[32.618, -30.231], [0, 0], [35.307, -30.421], [0, 0], [16.876, 3.708], [2.684, 23.993], [26.565, 40.024], [-56.684, 40.132], [-14.334, 1.133], [0, 0], [-24.629, -6.739], [-0.792, 1.007], [-20.485, 7.248], [-0.469, 1.19], [-11.944, 0.164], [2.598, 32.401]], "o": [[-9.366, 8.681], [0, 0], [-16.461, 14.183], [0, 0], [-15.907, -3.495], [0, 0], [-13.222, -19.92], [-23.654, 31.457], [18.09, -1.43], [0, 0], [13.9, 3.804], [-0.39, 0.944], [24.854, -8.793], [0, 0], [13.67, -0.188], [5.986, 26.364]], "v": [[105.884, 40.135], [94.173, 25.341], [65.205, 92.527], [45.755, 88.084], [6.65, 117.389], [-22.814, 70.873], [-52.884, 66.313], [-49.269, -79.876], [-38.354, 27.134], [-5.605, -20.545], [1.435, 46.731], [30.01, 6.458], [56.984, 45.21], [84.194, -36.647], [99.097, 3.423], [119.403, -50.247]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [{"i": [[37.207, -24.362], [0, 0], [55.675, -10.959], [0, 0], [18.895, 9.924], [-8.421, 20.939], [8.659, 48.961], [-62.574, 30.136], [-23.517, -6.491], [0, 0], [-23.163, -10.745], [-0.948, 0.861], [-21.405, 3.736], [-0.661, 1.095], [-11.805, -1.826], [-2.832, 32.382]], "o": [[-10.681, 7.001], [0, 0], [-21.318, 4.202], [0, 0], [-14.419, -7.572], [0, 0], [-4.174, -23.541], [-28.56, 27.081], [17.492, 4.828], [0, 0], [13.073, 6.064], [-0.542, 0.866], [25.971, -4.533], [0, 0], [13.51, 2.09], [1.513, 26.993]], "v": [[75.729, 55.289], [66.645, 38.752], [-4.799, 97.283], [-22.936, 83.097], [-75.32, 89.276], [-85.336, 32.323], [-113.652, 12.916], [-48.13, -97.907], [-90.77, -18.059], [-32.529, -45.39], [-44.767, 23.906], [4.983, -6.534], [6.889, 42.507], [76.275, -33.064], [84.3, 8.928], [114.318, -45.47]], "c": true}]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 151, "s": [{"i": [[46.032, -12.758], [0, 0], [49.827, 27.148], [0, 0], [7.591, 19.947], [-20.349, 7.625], [-36.481, 23.16], [-48.69, 14.602], [-21.843, -10.866], [0, 0], [-20.682, -14.974], [-1.095, 0.664], [-21.725, -0.424], [-0.858, 0.949], [-11.238, -4.049], [-16.972, 29.322]], "o": [[-12.307, 3.411], [0, 0], [-17.798, -9.697], [0, 0], [-5.551, -14.587], [0, 0], [22.597, -14.346], [-33.21, 21.123], [16.247, 8.082], [0, 0], [11.673, 8.452], [-0.697, 0.746], [26.359, 0.514], [0, 0], [12.862, 4.634], [-3.674, 26.784]], "v": [[25.667, 48.005], [22.197, 29.769], [-92.077, 46.149], [-96.017, 24.921], [-136.658, -3.113], [-105.429, -45.965], [-103.432, -86.593], [-30.016, -113.558], [-84.944, -63.939], [-39.145, -71.912], [-83.869, -16.137], [-12.414, -29.581], [-34.746, 8.786], [46.23, -40.52], [49.217, -3.252], [105.01, -43.924]], "c": true}]}, {"t": 179, "s": [{"i": [[37.207, -24.362], [0, 0], [55.675, -10.959], [0, 0], [18.895, 9.924], [-8.421, 20.939], [8.659, 48.961], [-62.574, 30.136], [-23.517, -6.491], [0, 0], [-23.163, -10.745], [-0.948, 0.861], [-21.405, 3.736], [-0.661, 1.095], [-11.805, -1.826], [-2.832, 32.382]], "o": [[-10.681, 7.001], [0, 0], [-21.318, 4.202], [0, 0], [-14.419, -7.572], [0, 0], [-4.174, -23.541], [-28.56, 27.081], [17.492, 4.828], [0, 0], [13.073, 6.064], [-0.542, 0.866], [25.971, -4.533], [0, 0], [13.51, 2.09], [1.513, 26.993]], "v": [[75.729, 55.289], [66.645, 38.752], [-4.799, 97.283], [-22.936, 83.097], [-75.32, 89.276], [-85.336, 32.323], [-113.652, 12.916], [-48.13, -97.907], [-90.77, -18.059], [-32.529, -45.39], [-44.767, 23.906], [4.983, -6.534], [6.889, 42.507], [76.275, -33.064], [84.3, 8.928], [114.318, -45.47]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.616, 0.255, 0.161, 0.512, 0.616, 0.255, 0.161, 1, 0.616, 0.255, 0.161, 0, 1, 0.512, 0.5, 1, 0]}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [4.963, 102.661], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [116.248, -114.643], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 29, "s": [-171.159, 18.151], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 132, "s": [-171.159, 18.151], "to": [0, 0], "ti": [0, 0]}, {"t": 151, "s": [4.963, 102.661]}]}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-3.799, -50.21], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [-19.134, 17.647], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 29, "s": [55.146, -61.274], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 132, "s": [55.146, -61.274], "to": [0, 0], "ti": [0, 0]}, {"t": 151, "s": [-3.799, -50.21]}]}, "t": 1, "nm": "35cum", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [126.982, 389.106]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 80}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-67.341, -20.147], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-7.348, 9.816]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [5.984, -7.993]], "v": [[35.167, -76.667], [65.036, -90.667], [83.208, -133.396], [119.353, -120.611], [112.219, -94.49], [138.215, -100.269], [143.981, -63.049], [98.037, -48.746], [93.587, -23.292], [57.04, 96.082], [47.953, 79.544], [-23.487, 138.076], [-41.63, 123.885], [-94.014, 130.068], [-104.034, 73.114], [-122.348, 48.703], [-55.323, -54.616], [-70.658, -69.321]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [42.877, -46.208], [0, 0], [39.068, -34.807], [0, 0], [18.605, 0.371], [1.647, 22.492], [25.937, 39.849], [-52.58, 43.109], [-1.051, 12.216]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-8.69, 9.365], [0, 0], [-14.966, 13.334], [0, 0], [-14.193, -0.283], [0, 0], [-13.932, -21.406], [0, 0], [1.051, -12.216]], "v": [[35.167, -85.167], [56.536, -91.167], [76.708, -147.896], [119.462, -120.021], [112.219, -94.49], [138.215, -100.269], [143.981, -63.049], [98.037, -48.746], [93.587, -23.292], [84.722, 68.924], [75.447, 66.19], [44.071, 121.266], [29.571, 115.869], [-7.77, 145.531], [-34.922, 97.831], [-66.08, 98.697], [-68.838, -38.841], [-83.867, -48.205]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[35.167, -85.167], [56.536, -91.167], [76.708, -147.896], [120.353, -120.611], [112.219, -94.49], [138.215, -100.269], [143.981, -63.049], [88.751, -57.074], [80.132, -23.307], [61.951, 78.964], [44.786, 67.554], [-21.516, 125.602], [-36.968, 110.925], [-94.014, 130.068], [-104.034, 73.114], [-132.348, 53.703], [-66.823, -57.116], [-79.158, -69.821]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 29, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.464, -8.836], [3.098, -1.53], [45.538, -17.796], [0, 0], [47.779, 27.443], [0, 0], [6.781, 18.147], [-20.568, 8.336], [-27.618, 37.084], [-67.467, 8.443], [-7.401, 9.776]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-11.899, 4.65], [0, 0], [-18.304, -10.513], [0, 0], [-5.173, -13.843], [0, 0], [14.836, -19.921], [0, 0], [7.401, -9.776]], "v": [[35.167, -85.167], [56.536, -91.167], [76.708, -147.896], [120.353, -120.611], [112.219, -94.49], [138.215, -100.269], [135.318, -71.586], [93.526, -55.637], [81.252, -26.485], [14.303, 71.854], [9.515, 54.095], [-105.096, 79.227], [-114.216, 65.855], [-156.319, 37.362], [-124.154, -5.734], [-131.17, -35.869], [-48.691, -73.591], [-56.415, -89.526]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[35.167, -85.167], [56.536, -91.167], [76.708, -147.896], [120.353, -120.611], [112.219, -94.49], [138.215, -100.269], [143.981, -63.049], [88.814, -55.531], [79.364, -23.052], [60.296, 78.243], [44.083, 66.163], [-21.381, 124.074], [-41.088, 109.735], [-91.213, 121.259], [-104.034, 73.114], [-132.348, 53.703], [-66.823, -57.116], [-79.158, -69.821]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 37, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [42.877, -46.208], [0, 0], [39.068, -34.807], [0, 0], [18.605, 0.371], [1.647, 22.492], [25.937, 39.849], [-52.58, 43.109], [-1.051, 12.216]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-8.69, 9.365], [0, 0], [-14.966, 13.334], [0, 0], [-14.193, -0.283], [0, 0], [-13.932, -21.406], [0, 0], [1.051, -12.216]], "v": [[35.167, -85.167], [56.536, -91.167], [76.708, -147.896], [120.353, -120.611], [112.219, -94.49], [138.215, -100.269], [143.981, -63.049], [92.274, -55.633], [81.214, -23.629], [86.198, 66.028], [73.153, 58.623], [40.204, 124.64], [22.515, 120.259], [-10.095, 143.563], [-41.682, 98.01], [-67.409, 98.146], [-68.838, -38.841], [-83.867, -48.205]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [21.628, -17.788], [0, 0], [34.074, -0.873], [0, 0], [17.578, 9.28], [3.347, 13.672], [22.979, 28.572], [-30.044, 12.974], [2.349, 7.663]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-4.383, 3.605], [0, 0], [-13.054, 0.335], [0, 0], [-13.41, -7.08], [0, 0], [-15.132, -18.815], [0, 0], [-2.349, -7.663]], "v": [[35.167, -85.167], [56.536, -91.167], [76.708, -147.896], [120.353, -120.611], [112.219, -94.49], [138.215, -100.269], [137.802, -71.13], [92.856, -50.29], [82.485, -15.858], [94.433, 91.901], [78.028, 90.124], [44.188, 103.14], [25.703, 82.372], [-9.811, 88.139], [-36.125, 43.177], [-83.933, 30.399], [-89.345, -49.735], [-103.566, -60.166]], "c": true}]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [33.164, -33.355], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-9.007, 9.059], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[35.167, -85.167], [56.536, -91.167], [76.708, -147.896], [120.353, -120.611], [112.219, -94.49], [138.215, -100.269], [143.981, -63.049], [88.814, -55.531], [79.364, -23.052], [60.296, 78.243], [44.083, 66.163], [-21.381, 124.074], [-41.088, 109.735], [-91.213, 121.259], [-104.034, 73.114], [-132.348, 53.703], [-66.823, -57.116], [-79.158, -69.821]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.33, "y": 0}, "t": 68, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.464, -8.836], [3.098, -1.53], [42.93, -51.194], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.802, 12.881], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[35.167, -85.167], [56.536, -91.167], [76.708, -147.896], [120.353, -120.611], [112.219, -94.49], [138.215, -100.269], [143.981, -63.049], [91.963, -57.754], [82.532, -24.086], [56.86, 60.292], [43.301, 53.84], [-33.838, 145.562], [-53.274, 138.149], [-100.952, 167.039], [-105.377, 122.727], [-130.819, 114.481], [-46.323, -28.709], [-56.709, -36.727]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [33.164, -33.355], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-9.007, 9.059], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[35.167, -85.167], [56.536, -91.167], [76.708, -147.896], [120.353, -120.611], [112.219, -94.49], [138.215, -100.269], [143.981, -63.049], [88.814, -55.531], [79.364, -23.052], [60.296, 78.243], [44.083, 66.163], [-21.381, 124.074], [-41.088, 109.735], [-91.213, 121.259], [-104.034, 73.114], [-132.348, 53.703], [-66.823, -57.116], [-79.158, -69.821]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.464, -8.836], [3.098, -1.53], [42.93, -51.194], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.802, 12.881], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[35.167, -85.167], [56.536, -91.167], [76.708, -147.896], [120.353, -120.611], [112.219, -94.49], [138.215, -100.269], [143.981, -63.049], [91.963, -57.754], [82.532, -24.086], [56.86, 60.292], [43.301, 53.84], [-33.838, 145.562], [-53.274, 138.149], [-100.952, 167.039], [-105.377, 122.727], [-130.819, 114.481], [-46.323, -28.709], [-56.709, -36.727]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [33.164, -33.355], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-9.007, 9.059], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[35.167, -85.167], [56.536, -91.167], [76.708, -147.896], [120.353, -120.611], [112.219, -94.49], [138.215, -100.269], [143.981, -63.049], [88.814, -55.531], [79.364, -23.052], [60.296, 78.243], [44.083, 66.163], [-21.381, 124.074], [-41.088, 109.735], [-91.213, 121.259], [-104.034, 73.114], [-132.348, 53.703], [-66.823, -57.116], [-79.158, -69.821]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.464, -8.836], [3.098, -1.53], [42.93, -51.194], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.802, 12.881], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[35.167, -85.167], [56.536, -91.167], [76.708, -147.896], [120.353, -120.611], [112.219, -94.49], [138.215, -100.269], [143.981, -63.049], [91.963, -57.754], [82.532, -24.086], [56.86, 60.292], [43.301, 53.84], [-33.838, 145.562], [-53.274, 138.149], [-100.952, 167.039], [-105.377, 122.727], [-130.819, 114.481], [-46.323, -28.709], [-56.709, -36.727]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [33.164, -33.355], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-9.007, 9.059], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[35.167, -85.167], [56.536, -91.167], [76.708, -147.896], [120.353, -120.611], [112.219, -94.49], [138.215, -100.269], [143.981, -63.049], [88.814, -55.531], [79.364, -23.052], [60.296, 78.243], [44.083, 66.163], [-21.381, 124.074], [-41.088, 109.735], [-91.213, 121.259], [-104.034, 73.114], [-132.348, 53.703], [-66.823, -57.116], [-79.158, -69.821]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.464, -8.836], [3.098, -1.53], [42.93, -51.194], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.802, 12.881], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[35.167, -85.167], [56.536, -91.167], [76.708, -147.896], [120.353, -120.611], [112.219, -94.49], [138.215, -100.269], [143.981, -63.049], [91.963, -57.754], [82.532, -24.086], [56.86, 60.292], [43.301, 53.84], [-33.838, 145.562], [-53.274, 138.149], [-100.952, 167.039], [-105.377, 122.727], [-130.819, 114.481], [-46.323, -28.709], [-56.709, -36.727]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [33.164, -33.355], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-9.007, 9.059], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[35.167, -85.167], [56.536, -91.167], [76.708, -147.896], [120.353, -120.611], [112.219, -94.49], [138.215, -100.269], [143.981, -63.049], [88.814, -55.531], [79.364, -23.052], [60.296, 78.243], [44.083, 66.163], [-21.381, 124.074], [-41.088, 109.735], [-91.213, 121.259], [-104.034, 73.114], [-132.348, 53.703], [-66.823, -57.116], [-79.158, -69.821]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.464, -8.836], [3.098, -1.53], [42.93, -51.194], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.802, 12.881], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[35.167, -85.167], [56.536, -91.167], [76.708, -147.896], [120.353, -120.611], [112.219, -94.49], [138.215, -100.269], [143.981, -63.049], [91.963, -57.754], [82.532, -24.086], [56.86, 60.292], [43.301, 53.84], [-33.838, 145.562], [-53.274, 138.149], [-100.952, 167.039], [-105.377, 122.727], [-130.819, 114.481], [-46.323, -28.709], [-56.709, -36.727]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [33.164, -33.355], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-9.007, 9.059], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[35.167, -85.167], [56.536, -91.167], [76.708, -147.896], [120.353, -120.611], [112.219, -94.49], [138.215, -100.269], [143.981, -63.049], [88.814, -55.531], [79.364, -23.052], [60.296, 78.243], [44.083, 66.163], [-21.381, 124.074], [-41.088, 109.735], [-91.213, 121.259], [-104.034, 73.114], [-132.348, 53.703], [-66.823, -57.116], [-79.158, -69.821]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.464, -8.836], [3.098, -1.53], [42.93, -51.194], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.802, 12.881], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[35.167, -85.167], [56.536, -91.167], [76.708, -147.896], [120.353, -120.611], [112.219, -94.49], [138.215, -100.269], [143.981, -63.049], [91.963, -57.754], [82.532, -24.086], [56.86, 60.292], [43.301, 53.84], [-33.838, 145.562], [-53.274, 138.149], [-100.952, 167.039], [-105.377, 122.727], [-130.819, 114.481], [-46.323, -28.709], [-56.709, -36.727]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 123, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [33.164, -33.355], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-9.007, 9.059], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[35.167, -85.167], [56.536, -91.167], [76.708, -147.896], [120.353, -120.611], [112.219, -94.49], [138.215, -100.269], [143.981, -63.049], [88.814, -55.531], [79.364, -23.052], [60.296, 78.243], [44.083, 66.163], [-21.381, 124.074], [-41.088, 109.735], [-91.213, 121.259], [-104.034, 73.114], [-132.348, 53.703], [-66.823, -57.116], [-79.158, -69.821]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 131, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [25.081, -49.247], [0, 0], [39.068, -34.807], [0, 0], [18.605, 0.371], [1.647, 22.492], [25.937, 39.849], [-52.58, 43.109], [-1.051, 12.216]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-5.798, 11.384], [0, 0], [-14.966, 13.334], [0, 0], [-14.193, -0.283], [0, 0], [-13.932, -21.406], [0, 0], [1.051, -12.216]], "v": [[35.167, -85.167], [56.536, -91.167], [76.708, -147.896], [114.176, -119.96], [106.901, -90.432], [132.135, -93.482], [132.942, -64.433], [92.829, -53.594], [87.493, -23.749], [85.835, 67.618], [70.156, 62.45], [41.345, 128.881], [26.861, 129.205], [-11.516, 158.27], [-41.638, 111.895], [-71.296, 107.28], [-68.838, -38.841], [-83.867, -48.205]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [{"i": [[-67.341, -20.147], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-7.348, 9.816]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [5.984, -7.993]], "v": [[35.167, -76.667], [65.036, -90.667], [83.208, -133.396], [119.353, -120.611], [112.219, -94.49], [138.215, -100.269], [143.981, -63.049], [98.037, -48.746], [93.587, -23.292], [57.04, 96.082], [47.953, 79.544], [-23.487, 138.076], [-41.63, 123.885], [-94.014, 130.068], [-104.034, 73.114], [-122.348, 48.703], [-55.323, -54.616], [-70.658, -69.321]], "c": true}]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 151, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.464, -8.836], [3.098, -1.53], [60.909, -16.238], [0, 0], [47.779, 27.443], [0, 0], [6.781, 18.147], [-20.568, 8.336], [-36.681, 28.151], [-31.754, 9.7], [-7.401, 9.776]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-12.344, 3.291], [0, 0], [-18.304, -10.513], [0, 0], [-5.173, -13.843], [0, 0], [19.704, -15.122], [0, 0], [7.401, -9.776]], "v": [[32.396, -74.617], [60.819, -86.434], [81.663, -132.836], [120.353, -120.611], [112.219, -94.49], [138.215, -100.269], [143.981, -63.049], [98.037, -48.746], [93.587, -23.292], [7.039, 88.858], [3.602, 70.304], [-110.932, 86.3], [-114.216, 65.855], [-149.734, 37.625], [-113.434, -8.333], [-113.713, -39.467], [-41.34, -64.218], [-42.105, -84.753]], "c": true}]}, {"t": 179, "s": [{"i": [[-67.341, -20.147], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-7.348, 9.816]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [5.984, -7.993]], "v": [[35.167, -76.667], [65.036, -90.667], [83.208, -133.396], [119.353, -120.611], [112.219, -94.49], [138.215, -100.269], [143.981, -63.049], [98.037, -48.746], [93.587, -23.292], [57.04, 96.082], [47.953, 79.544], [-23.487, 138.076], [-41.63, 123.885], [-94.014, 130.068], [-104.034, 73.114], [-122.348, 48.703], [-55.323, -54.616], [-70.658, -69.321]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 18}, "g": {"p": 3, "k": {"a": 0, "k": [0.094, 1, 1, 0.922, 0.532, 1, 1, 0.922, 1, 1, 1, 0.922, 0.094, 0, 0.532, 0.5, 1, 1]}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-22.369, 132.518], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [120.153, -117.01], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 29, "s": [-119.027, -19.844], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 132, "s": [-119.027, -19.844], "to": [0, 0], "ti": [0, 0]}, {"t": 149, "s": [-22.369, 132.518]}]}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-2.119, -126.136], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [-86.119, 89.771], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 29, "s": [107.198, 45.969], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 132, "s": [107.198, 45.969], "to": [0, 0], "ti": [0, 0]}, {"t": 149, "s": [-2.119, -126.136]}]}, "t": 1, "lc": 2, "lj": 2, "bm": 0, "nm": "35cock", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [145.674, 348.315]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 50}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 6", "bm": 0, "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "det", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [42.877, -46.208], [0, 0], [39.068, -34.807], [0, 0], [18.605, 0.371], [1.647, 22.492], [25.937, 39.849], [-52.58, 43.109], [-1.051, 12.216]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-8.69, 9.365], [0, 0], [-14.966, 13.334], [0, 0], [-14.193, -0.283], [0, 0], [-13.932, -21.406], [0, 0], [1.051, -12.216]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [82.958, 84.654], [70.159, 70.789], [41.265, 138.327], [21.573, 133.803], [-16.804, 162.869], [-46.925, 116.494], [-76.583, 111.879], [-74.125, -34.243], [-89.155, -43.606]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 29, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.464, -8.836], [3.098, -1.53], [60.909, -16.238], [0, 0], [47.779, 27.443], [0, 0], [6.781, 18.147], [-20.568, 8.336], [-27.618, 37.084], [-67.467, 8.443], [-7.401, 9.776]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-12.344, 3.291], [0, 0], [-18.304, -10.513], [0, 0], [-5.173, -13.843], [0, 0], [14.836, -19.921], [0, 0], [7.401, -9.776]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [1.752, 93.457], [-1.686, 74.903], [-116.22, 90.899], [-119.503, 70.454], [-161.606, 41.96], [-129.441, -1.136], [-136.457, -31.27], [-53.978, -68.993], [-61.703, -84.927]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 37, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [42.877, -46.208], [0, 0], [39.068, -34.807], [0, 0], [18.605, 0.371], [1.647, 22.492], [25.937, 39.849], [-52.58, 43.109], [-1.051, 12.216]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-8.69, 9.365], [0, 0], [-14.966, 13.334], [0, 0], [-14.193, -0.283], [0, 0], [-13.932, -21.406], [0, 0], [1.051, -12.216]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [82.958, 84.654], [70.159, 70.789], [41.265, 138.327], [21.573, 133.803], [-16.804, 162.869], [-46.925, 116.494], [-76.583, 111.879], [-74.125, -34.243], [-89.155, -43.606]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 50, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [21.628, -17.788], [0, 0], [34.074, -0.873], [0, 0], [17.578, 9.28], [3.347, 13.672], [22.979, 28.572], [-30.044, 12.974], [2.349, 7.663]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-4.383, 3.605], [0, 0], [-13.054, 0.335], [0, 0], [-13.41, -7.08], [0, 0], [-15.132, -18.815], [0, 0], [-2.349, -7.663]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [95.663, 119.155], [82.023, 106.43], [33.322, 118.619], [17.758, 94.605], [-19.493, 104.357], [-45.649, 54.425], [-89.221, 34.998], [-94.632, -45.136], [-108.853, -55.567]], "c": true}]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.33, "y": 0}, "t": 68, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [59.013, -58.294], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-11.96, 11.814], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [48.458, 83.108], [41.988, 69.721], [-39.889, 160.933], [-55.904, 153.823], [-109.898, 182.428], [-110.664, 127.326], [-136.106, 119.08], [-51.611, -24.11], [-61.996, -32.129]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [59.013, -58.294], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-11.96, 11.814], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [48.458, 83.108], [41.988, 69.721], [-39.889, 160.933], [-55.904, 153.823], [-109.898, 182.428], [-110.664, 127.326], [-136.106, 119.08], [-51.611, -24.11], [-61.996, -32.129]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [59.013, -58.294], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-11.96, 11.814], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [48.458, 83.108], [41.988, 69.721], [-39.889, 160.933], [-55.904, 153.823], [-109.898, 182.428], [-110.664, 127.326], [-136.106, 119.08], [-51.611, -24.11], [-61.996, -32.129]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [59.013, -58.294], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-11.96, 11.814], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [48.458, 83.108], [41.988, 69.721], [-39.889, 160.933], [-55.904, 153.823], [-109.898, 182.428], [-110.664, 127.326], [-136.106, 119.08], [-51.611, -24.11], [-61.996, -32.129]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [59.013, -58.294], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-11.96, 11.814], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [48.458, 83.108], [41.988, 69.721], [-39.889, 160.933], [-55.904, 153.823], [-109.898, 182.428], [-110.664, 127.326], [-136.106, 119.08], [-51.611, -24.11], [-61.996, -32.129]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [59.013, -58.294], [0, 0], [58.105, -34.97], [0, 0], [17.478, 2.342], [-11.961, 25.382], [0.71, 47.355], [-66.905, 56.397], [-5.913, 13.766]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-11.96, 11.814], [0, 0], [-22.259, 13.397], [0, 0], [-13.333, -1.787], [0, 0], [-0.381, -25.438], [0, 0], [5.913, -13.766]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [48.458, 83.108], [41.988, 69.721], [-39.889, 160.933], [-55.904, 153.823], [-109.898, 182.428], [-110.664, 127.326], [-136.106, 119.08], [-51.611, -24.11], [-61.996, -32.129]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 123, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 131, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [42.877, -46.208], [0, 0], [39.068, -34.807], [0, 0], [18.605, 0.371], [1.647, 22.492], [25.937, 39.849], [-52.58, 43.109], [-1.051, 12.216]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-8.69, 9.365], [0, 0], [-14.966, 13.334], [0, 0], [-14.193, -0.283], [0, 0], [-13.932, -21.406], [0, 0], [1.051, -12.216]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [82.958, 84.654], [70.159, 70.789], [41.265, 138.327], [21.573, 133.803], [-16.804, 162.869], [-46.925, 116.494], [-76.583, 111.879], [-74.125, -34.243], [-89.155, -43.606]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 151, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.464, -8.836], [3.098, -1.53], [60.909, -16.238], [0, 0], [47.779, 27.443], [0, 0], [6.781, 18.147], [-20.568, 8.336], [-36.681, 28.151], [-31.249, 9.494], [-7.401, 9.776]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-12.344, 3.291], [0, 0], [-18.304, -10.513], [0, 0], [-5.173, -13.843], [0, 0], [19.704, -15.122], [0, 0], [7.401, -9.776]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.927, -95.67], [138.693, -58.45], [92.75, -44.147], [88.299, -18.693], [1.752, 93.457], [-1.686, 74.903], [-116.22, 90.899], [-119.503, 70.454], [-161.606, 41.96], [-129.441, -1.136], [-126.333, -41.305], [-53.978, -68.993], [-61.703, -84.927]], "c": true}]}, {"t": 179, "s": [{"i": [[-62.181, -25.898], [-4.941, 5.614], [-5.177, 25.21], [-16.299, -19.126], [0, 0], [0, 0], [-0.542, -13.481], [20.465, -8.836], [3.098, -1.53], [52.735, -34.534], [0, 0], [55.672, -10.956], [0, 0], [18.895, 9.921], [-8.421, 20.941], [8.665, 48.968], [-61.408, 29.192], [-3.959, 11.605]], "o": [[0, 0], [9.527, -10.824], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.328, 1.437], [0, 0], [-10.687, 6.999], [0, 0], [-21.327, 4.197], [0, 0], [-14.415, -7.568], [0, 0], [-4.655, -26.304], [0, 0], [3.959, -11.605]], "v": [[29.879, -80.569], [51.249, -86.569], [71.42, -143.298], [115.066, -116.013], [106.932, -89.892], [132.928, -95.67], [138.694, -58.45], [92.75, -44.147], [88.299, -18.693], [51.752, 100.681], [42.665, 84.143], [-28.775, 142.675], [-46.918, 128.483], [-99.301, 134.666], [-109.322, 77.713], [-137.635, 58.302], [-72.11, -52.518], [-84.446, -65.222]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0.28, 0.878, 0.6, 0.196, 0.631, 0.847, 0.518, 0.204, 0.999, 0.816, 0.435, 0.212]}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.66, "y": 0}, "t": 0, "s": [1.962, -143.851], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [-138.162, 97.679], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 29, "s": [40.427, 79.821], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 132, "s": [40.427, 79.821], "to": [0, 0], "ti": [0, 0]}, {"t": 149, "s": [1.962, -143.851]}]}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.66, "y": 0}, "t": 0, "s": [2.755, 140.651], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [142.976, -131.717], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 29, "s": [-116.67, -61.595], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.12, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 132, "s": [-116.67, -61.595], "to": [0, 0], "ti": [0, 0]}, {"t": 149, "s": [2.755, 140.651]}]}, "t": 1, "nm": "35wtf", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [150.962, 343.716]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 8", "bm": 0, "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 65, "ty": 4, "nm": "stik", "parent": 58, "sr": 1, "ks": {"r": {"a": 0, "k": 51.344}, "p": {"a": 0, "k": [47.71, 0.865, 0]}, "a": {"a": 0, "k": [323.477, 158.283, 0]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-18.266, 12.221], [-31.119, 36.379], [4.242, -7.862], [98.965, -60.735], [41.609, -40.876]], "o": [[78.553, -86.03], [51.317, -34.334], [9.548, -11.162], [-4.242, 7.862], [-15.285, 9.381], [0, 0]], "v": [[-156.137, 139.321], [-22.69, -35.619], [119.072, -155.787], [155.744, -128.145], [4.775, -7.157], [-131.42, 158.188]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 18}, "g": {"p": 3, "k": {"a": 0, "k": [0.495, 0.239, 0.098, 0.082, 0.68, 0.371, 0.192, 0.122, 1, 0.502, 0.286, 0.161]}}, "s": {"a": 0, "k": [1253.413, -2410.883]}, "e": {"a": 0, "k": [1172.133, -2609.008]}, "t": 1, "lc": 2, "lj": 2, "bm": 0, "nm": "35wuhsdsf", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [339.477, 168.283]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [-18.266, 12.221], [-31.119, 36.379], [4.242, -7.862], [98.964, -60.735], [41.609, -40.876]], "o": [[78.553, -86.03], [51.317, -34.334], [9.548, -11.162], [-4.242, 7.862], [-15.285, 9.381], [0, 0]], "v": [[-187.587, 177.521], [-47.64, 13.581], [90.622, -103.587], [124.294, -89.945], [-26.676, 31.043], [-162.871, 196.388]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [{"i": [[0, 0], [-18.266, 12.221], [-31.119, 36.379], [4.242, -7.862], [98.964, -60.735], [41.609, -40.876]], "o": [[78.553, -86.03], [51.317, -34.334], [9.548, -11.162], [-4.242, 7.862], [-15.285, 9.381], [0, 0]], "v": [[-187.587, 177.521], [-54.14, 2.581], [87.622, -117.587], [124.294, -89.945], [-26.676, 31.043], [-162.871, 196.388]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [{"i": [[0, 0], [-18.266, 12.221], [-31.119, 36.379], [4.242, -7.862], [98.964, -60.735], [41.609, -40.876]], "o": [[78.552, -86.03], [51.317, -34.334], [9.548, -11.162], [-4.242, 7.862], [-15.285, 9.381], [0, 0]], "v": [[-187.587, 177.521], [-54.14, 2.581], [87.622, -117.587], [113.185, -93.554], [-34.779, 22.632], [-162.871, 196.388]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55, "s": [{"i": [[0, 0], [-18.266, 12.221], [-31.119, 36.379], [4.242, -7.862], [98.964, -60.735], [41.609, -40.876]], "o": [[78.552, -86.03], [51.317, -34.334], [9.548, -11.162], [-4.242, 7.862], [-15.285, 9.381], [0, 0]], "v": [[-187.587, 177.521], [-54.14, 2.581], [87.622, -117.587], [113.185, -93.554], [-34.779, 22.632], [-162.871, 196.388]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 125, "s": [{"i": [[0, 0], [-18.266, 12.221], [-31.119, 36.379], [4.242, -7.862], [98.964, -60.735], [41.609, -40.876]], "o": [[78.552, -86.03], [51.317, -34.334], [9.548, -11.162], [-4.242, 7.862], [-15.285, 9.381], [0, 0]], "v": [[-187.587, 177.521], [-54.14, 2.581], [87.622, -117.587], [113.185, -93.554], [-34.779, 22.632], [-162.871, 196.388]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 132, "s": [{"i": [[0, 0], [-18.266, 12.221], [-31.119, 36.379], [4.242, -7.862], [98.964, -60.735], [41.609, -40.876]], "o": [[78.553, -86.03], [51.317, -34.334], [9.548, -11.162], [-4.242, 7.862], [-15.285, 9.381], [0, 0]], "v": [[-187.587, 177.521], [-54.14, 2.581], [79.802, -103.697], [109.531, -83.347], [-26.676, 31.043], [-162.871, 196.388]], "c": true}]}, {"t": 145, "s": [{"i": [[0, 0], [-18.266, 12.221], [-31.119, 36.379], [4.242, -7.862], [98.964, -60.735], [41.609, -40.876]], "o": [[78.553, -86.03], [51.317, -34.334], [9.548, -11.162], [-4.242, 7.862], [-15.285, 9.381], [0, 0]], "v": [[-187.587, 177.521], [-47.64, 13.581], [90.622, -103.587], [124.294, -89.945], [-26.676, 31.043], [-162.871, 196.388]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 18}, "g": {"p": 3, "k": {"a": 0, "k": [0.091, 1, 0.863, 0.596, 0.477, 1, 0.863, 0.625, 1, 1, 0.863, 0.655, 0.091, 0, 0.477, 0.5, 1, 1]}}, "s": {"a": 0, "k": [-5.265, 265.345]}, "e": {"a": 0, "k": [-13.915, 49.233]}, "t": 1, "lc": 2, "lj": 2, "bm": 0, "nm": "35yfbhsd", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [370.927, 130.083]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 50}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-18.266, 12.221], [-31.119, 36.379], [4.242, -7.862], [98.965, -60.735], [41.609, -40.876]], "o": [[78.553, -86.03], [51.317, -34.334], [9.548, -11.162], [-4.242, 7.862], [-15.285, 9.381], [0, 0]], "v": [[-156.137, 139.321], [-22.69, -35.619], [119.072, -155.787], [155.744, -128.145], [4.775, -7.157], [-131.42, 158.188]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.714, 0.506, 0.298, 0.425, 0.602, 0.38, 0.249, 0.976, 0.49, 0.255, 0.2]}}, "s": {"a": 0, "k": [15.523, -146.283]}, "e": {"a": 0, "k": [-4.647, 41.008]}, "t": 1, "nm": "35s.df.g,gd", "hd": false, "cl": "df g"}, {"ty": "tr", "p": {"a": 0, "k": [339.477, 168.283]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 66, "ty": 4, "nm": "fx", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 16, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 17, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 159, "s": [0]}, {"t": 160, "s": [100]}]}, "p": {"a": 0, "k": [350.781, 401.465, 0]}, "a": {"a": 0, "k": [355.769, 409.121, 0]}, "s": {"a": 0, "k": [95, 95, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.753, 0.01], [6.01, 4.086], [0, 13.709], [0.744, 0.018], [0.035, -0.744], [3.822, -8.99], [9.374, -1.111], [0.158, -0.026], [-0.057, -0.712], [-0.714, 0], [-1.077, 0.116], [-5.464, -4.666], [0, -13.736], [-0.744, -0.017], [-0.011, 0], [-0.034, 0.733], [-21.661, 0], [-0.005, 0.753]], "o": [[-0.06, -0.001], [-8.122, -5.522], [0, -0.744], [-0.738, -0.002], [-0.004, 0.091], [-4.484, 10.55], [-2.069, -0.022], [-0.705, 0.114], [0.057, 0.712], [1.128, 0], [3.736, 0.052], [6.777, 5.787], [0, 0.744], [0.011, 0], [0.73, 0], [0.017, -0.376], [0.753, 0], [0.005, -0.753]], "v": [[27.8, -3.544], [15.71, -7.759], [3.469, -36.741], [2.131, -38.111], [0.731, -36.804], [-3.555, -18.57], [-24.425, -1.02], [-28, -0.809], [-29.146, 0.652], [-27.781, 1.912], [-24.472, 1.738], [-8.808, 7.319], [1.406, 36.741], [2.744, 38.11], [2.776, 38.111], [4.144, 36.804], [27.781, -0.805], [29.151, -2.165]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313726425, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [413.23, 434.653]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.88, 0.88], "y": [0, 0]}, "t": 0, "s": [100, 100]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 10.857, "s": [0, 201]}, {"i": {"x": [0.12, 0.12], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 163.223, "s": [0, 201]}, {"t": 180, "s": [100, 100]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.256, 0.003], [2.047, 1.392], [0, 4.669], [0.253, 0.006], [0.012, -0.253], [1.301, -3.062], [3.192, -0.378], [0.054, -0.009], [-0.02, -0.242], [-0.243, 0], [-0.367, 0.04], [-1.861, -1.589], [0, -4.678], [-0.253, -0.006], [-0.004, 0], [-0.012, 0.25], [-7.377, 0], [-0.002, 0.256]], "o": [[-0.02, 0], [-2.766, -1.88], [0, -0.253], [-0.251, -0.001], [-0.001, 0.031], [-1.527, 3.593], [-0.705, -0.007], [-0.24, 0.039], [0.02, 0.242], [0.384, 0], [1.272, 0.018], [2.308, 1.971], [0, 0.253], [0.004, 0], [0.249, 0], [0.006, -0.128], [0.256, 0], [0.002, -0.256]], "v": [[9.468, -1.207], [5.351, -2.643], [1.182, -12.513], [0.726, -12.98], [0.249, -12.534], [-1.211, -6.325], [-8.318, -0.348], [-9.536, -0.276], [-9.926, 0.222], [-9.461, 0.651], [-8.335, 0.592], [-3, 2.493], [0.479, 12.513], [0.935, 12.979], [0.945, 12.98], [1.411, 12.534], [9.461, -0.274], [9.928, -0.737]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313726425, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [477.017, 339.45]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 0, "s": [100, 100]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 8, "s": [0, 201]}, {"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 159.334, "s": [0, 201]}, {"t": 180, "s": [100, 100]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.348, 0.005], [2.775, 1.887], [0, 6.329], [0.344, 0.008], [0.016, -0.343], [1.764, -4.151], [4.328, -0.513], [0.073, -0.012], [-0.026, -0.329], [-0.33, 0], [-0.497, 0.054], [-2.523, -2.154], [0, -6.342], [-0.344, -0.008], [-0.005, 0], [-0.016, 0.338], [-10.001, 0], [-0.002, 0.348]], "o": [[-0.028, 0], [-3.75, -2.549], [0, -0.344], [-0.341, -0.001], [-0.002, 0.042], [-2.07, 4.871], [-0.955, -0.01], [-0.325, 0.053], [0.026, 0.329], [0.521, 0], [1.725, 0.024], [3.129, 2.672], [0, 0.344], [0.005, 0], [0.337, 0], [0.008, -0.174], [0.348, 0], [0.002, -0.348]], "v": [[12.835, -1.636], [7.253, -3.582], [1.602, -16.963], [0.984, -17.595], [0.338, -16.992], [-1.642, -8.574], [-11.277, -0.471], [-12.927, -0.374], [-13.457, 0.301], [-12.826, 0.883], [-11.299, 0.802], [-4.067, 3.379], [0.649, 16.963], [1.267, 17.595], [1.282, 17.595], [1.913, 16.992], [12.826, -0.372], [13.459, -1]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313726425, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [326.369, 395.416]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 0, "s": [100, 100]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 13.143, "s": [201, 0]}, {"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 166, "s": [201, 0]}, {"t": 180, "s": [100, 100]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.564, 0.008], [4.504, 3.062], [0, 10.274], [0.557, 0.013], [0.026, -0.557], [2.864, -6.737], [7.025, -0.832], [0.118, -0.019], [-0.043, -0.533], [-0.535, 0], [-0.807, 0.087], [-4.094, -3.496], [0, -10.293], [-0.558, -0.013], [-0.008, 0], [-0.025, 0.549], [-16.232, 0], [-0.004, 0.564]], "o": [[-0.045, -0.001], [-6.087, -4.138], [0, -0.558], [-0.553, -0.001], [-0.003, 0.068], [-3.361, 7.906], [-1.551, -0.016], [-0.528, 0.085], [0.043, 0.533], [0.845, 0], [2.8, 0.039], [5.079, 4.337], [0, 0.558], [0.008, 0], [0.547, 0], [0.013, -0.282], [0.564, 0], [0.004, -0.564]], "v": [[20.833, -2.656], [11.773, -5.815], [2.6, -27.534], [1.597, -28.56], [0.548, -27.58], [-2.665, -13.916], [-18.304, -0.765], [-20.983, -0.607], [-21.842, 0.489], [-20.819, 1.433], [-18.339, 1.302], [-6.601, 5.485], [1.053, 27.533], [2.056, 28.559], [2.08, 28.56], [3.105, 27.58], [20.819, -0.603], [21.845, -1.622]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313726425, 1, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [246.438, 463.212]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 0, "s": [100, 100]}, {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 16, "s": [201, 0]}, {"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 169.334, "s": [201, 0]}, {"t": 180, "s": [100, 100]}]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4", "bm": 0, "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 67, "ty": 4, "nm": "woosh", "parent": 65, "sr": 1, "ks": {"o": {"a": 0, "k": 60}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 50, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 61, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 72, "s": [10.538]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 90, "s": [-14.366]}, {"i": {"x": [0.12], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 115, "s": [10.538]}, {"t": 133, "s": [0]}]}, "p": {"a": 0, "k": [221.597, 280.81, 0]}, "a": {"a": 0, "k": [-57.407, -114.607, 0]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [-53.352, 61.065], [118.065, -62.853], [0, 0]], "o": [[-8.789, 96.698], [53.352, -61.065], [-128.774, 68.554], [0, 0]], "v": [[-10.501, -113.435], [155.185, -61.042], [128.798, 82.966], [-206.35, 82.966]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [{"i": [[0, 0], [-68.14, 43.958], [130.83, -27.808], [0, 0]], "o": [[-8.789, 96.698], [68.14, -43.958], [-142.697, 30.33], [0, 0]], "v": [[-10.501, -113.435], [182.013, 8.51], [116.886, 139.633], [-140.264, 83.631]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[0, 0], [-67.106, 82.807], [100.979, -87.709], [0, 0]], "o": [[-8.789, 96.698], [51.054, -62.999], [-176.229, 153.07], [0, 0]], "v": [[-10.501, -113.435], [238.552, -97.448], [212.165, 46.56], [-140.264, 83.631]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 29, "s": [{"i": [[0, 0], [-67.106, 82.807], [100.979, -87.709], [0, 0]], "o": [[-8.789, 96.698], [51.054, -62.999], [-176.229, 153.07], [0, 0]], "v": [[-10.501, -113.435], [238.552, -97.448], [212.165, 46.56], [-254.86, -22.685]], "c": true}]}, {"t": 37, "s": [{"i": [[0, 0], [-53.352, 61.065], [118.065, -62.853], [0, 0]], "o": [[-8.789, 96.698], [53.352, -61.065], [-128.774, 68.554], [0, 0]], "v": [[-10.501, -113.435], [155.185, -61.042], [128.798, 82.966], [-166.676, 24.791]], "c": true}], "h": 1}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-39.711, -84.966], [-64.264, 118.572], [-325.533, -57.029], [-86.379, -139.731]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [{"i": [[0, 0], [228.742, -123.718], [-8.257, 81.679], [0, 0]], "o": [[0, 0], [-64.215, 34.732], [27.164, -268.707], [0, 0]], "v": [[-39.711, -84.966], [-177.079, 162.174], [-305.19, 41.417], [-86.379, -139.731]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 115, "s": [{"i": [[0, 0], [228.742, -123.718], [-8.257, 81.679], [0, 0]], "o": [[0, 0], [-64.215, 34.732], [27.164, -268.707], [0, 0]], "v": [[-39.711, -84.966], [-177.079, 162.174], [-305.19, 41.417], [-86.379, -139.731]], "c": true}]}, {"i": {"x": 0.833, "y": 0.556}, "o": {"x": 0.333, "y": 0}, "t": 123, "s": [{"i": [[0, 0], [228.742, -123.718], [-8.257, 81.679], [0, 0]], "o": [[0, 0], [-64.215, 34.732], [27.164, -268.707], [0, 0]], "v": [[-39.711, -84.966], [-177.079, 162.174], [-305.19, 41.417], [-86.379, -139.731]], "c": true}]}, {"t": 126, "s": [{"i": [[0, 0], [67.958, -148.652], [47.376, 103.042], [0, 0]], "o": [[0, 0], [-30.354, 66.397], [-47.306, -200.425], [0, 0]], "v": [[-39.711, -84.966], [-9.635, 176.414], [-221.786, 71.385], [-86.379, -139.731]], "c": true}], "h": 1}, {"i": {"x": 0.34, "y": 1}, "o": {"x": 0.66, "y": 0}, "t": 127, "s": [{"i": [[0, 0], [-93.05, 115.817], [112.359, -72.56], [0, 0]], "o": [[-8.789, 96.698], [50.788, -63.214], [-338.691, 218.723], [0, 0]], "v": [[-37.327, -122.255], [207.03, -130.766], [193.24, 59.013], [-161.944, -48.321]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.66, "y": 0}, "t": 143, "s": [{"i": [[0, 0], [-93.05, 115.817], [112.359, -72.56], [0, 0]], "o": [[-8.789, 96.698], [50.788, -63.214], [-338.691, 218.723], [0, 0]], "v": [[-10.501, -113.435], [207.03, -130.766], [193.24, 59.013], [-239.352, 46.277]], "c": true}]}, {"i": {"x": 0.34, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [{"i": [[0, 0], [-90.314, 112.044], [112.753, -71.891], [0, 0]], "o": [[-8.789, 96.698], [50.964, -63.066], [-324.227, 208.376], [0, 0]], "v": [[-10.501, -113.435], [203.458, -125.962], [188.799, 60.664], [-268.539, -15.376]], "c": true}]}, {"t": 179, "s": [{"i": [[0, 0], [-53.352, 61.065], [118.065, -62.853], [0, 0]], "o": [[-8.789, 96.698], [53.352, -61.065], [-128.774, 68.554], [0, 0]], "v": [[-10.501, -113.435], [155.185, -61.042], [128.798, 82.966], [-206.35, 82.966]], "c": true}]}]}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.522, 1, 0, 0.478, 0.522, 1, 0, 0.964, 0.522, 1, 0, 0, 1, 0.478, 0.5, 0.964, 0]}}, "s": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [-207.003, -0.416], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [-207.003, -0.416], "to": [0, 0], "ti": [0, 0]}, {"t": 30, "s": [-755.89, -229.186], "h": 1}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [-15.256, -173.82], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [-66.422, -104.101], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 115, "s": [-66.422, -104.101], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 123, "s": [-66.422, -104.101], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.649, "y": 0.962}, "o": {"x": 0.301, "y": 0}, "t": 124.125, "s": [-66.422, -104.101], "to": [0, 0], "ti": [0, 0]}, {"t": 126, "s": [124.966, -627.135], "h": 1}, {"i": {"x": 0.658, "y": 0.996}, "o": {"x": 0.323, "y": 0.046}, "t": 129, "s": [-23.006, -506.71], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.314, "y": 1}, "t": 130, "s": [-300.763, -40.89], "to": [0, 0], "ti": [0, 0]}, {"t": 179, "s": [-207.003, -0.416]}]}, "e": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [205.697, -0.416], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [205.697, -0.416], "to": [0, 0], "ti": [0, 0]}, {"t": 30, "s": [-264.051, -93.156], "h": 1}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [-48.463, -128.59], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [-283.384, 117.82], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 115, "s": [-283.384, 117.82], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 123, "s": [-283.384, 117.82], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.649, "y": 0.912}, "o": {"x": 0.301, "y": 0}, "t": 124.125, "s": [-253.134, 88.737], "to": [0, 0], "ti": [0, 0]}, {"t": 126, "s": [68.124, -479.288], "h": 1}, {"i": {"x": 0.654, "y": 0.994}, "o": {"x": 0.32, "y": 0.033}, "t": 129, "s": [-41.972, -272.096], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.373, "y": 0.283}, "t": 130, "s": [-117.534, 16.073], "to": [0, 0], "ti": [0, 0]}, {"t": 179, "s": [205.697, -0.416]}]}, "t": 1, "nm": "35isdkfhjdng", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}], "ip": 0, "op": 180, "st": 0, "bm": 0}, {"ddd": 0, "ind": 68, "ty": 4, "nm": "speedmid 5", "parent": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-251, -38, 0]}, "a": {"a": 0, "k": [-251, 42, 0]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-251, 42], [249.5, 42]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 65, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 72, "s": [100]}, {"t": 74, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 75, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 82, "s": [100]}, {"t": 84, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 85, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 92, "s": [100]}, {"t": 94, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 95, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 102, "s": [100]}, {"t": 104, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 112, "s": [100]}, {"t": 114, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 115, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 122, "s": [100]}, {"t": 124, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 125, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 132, "s": [100]}, {"t": 134, "s": [100], "h": 1}]}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 65, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 68, "s": [0]}, {"t": 74, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 75, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 78, "s": [0]}, {"t": 84, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 85, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 88, "s": [0]}, {"t": 94, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 95, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 98, "s": [0]}, {"t": 104, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 108, "s": [0]}, {"t": 114, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 115, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [0]}, {"t": 124, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 125, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 128, "s": [0]}, {"t": 134, "s": [100], "h": 1}]}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 35}, "w": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 65, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 69, "s": [13.5]}, {"t": 74, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 75, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 79, "s": [13.5]}, {"t": 84, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 85, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 89, "s": [13.5]}, {"t": 94, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 95, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 99, "s": [13.5]}, {"t": 104, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 109, "s": [13.5]}, {"t": 114, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 115, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 119, "s": [13.5]}, {"t": 124, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 125, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 129, "s": [13.5]}, {"t": 134, "s": [0], "h": 1}]}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Shape 1", "bm": 0, "hd": false}], "ip": 65, "op": 135, "st": 1, "bm": 0}, {"ddd": 0, "ind": 69, "ty": 4, "nm": "speedmid 3", "parent": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-251, -118, 0]}, "a": {"a": 0, "k": [-251, 42, 0]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-251, 42], [249.5, 42]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 68, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 75, "s": [100]}, {"t": 77, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 78, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 85, "s": [100]}, {"t": 87, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 88, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 95, "s": [100]}, {"t": 97, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 98, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105, "s": [100]}, {"t": 107, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 108, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 115, "s": [100]}, {"t": 117, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 125, "s": [100]}, {"t": 127, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 128, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 135, "s": [100]}, {"t": 137, "s": [100], "h": 1}]}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 68, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 69, "s": [0]}, {"t": 77, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 78, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 79, "s": [0]}, {"t": 87, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 88, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [0]}, {"t": 97, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 98, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 99, "s": [0]}, {"t": 107, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 108, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 109, "s": [0]}, {"t": 117, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 119, "s": [0]}, {"t": 127, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 128, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 129, "s": [0]}, {"t": 137, "s": [100], "h": 1}]}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 35}, "w": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 68, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 72, "s": [13.5]}, {"t": 77, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 78, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 82, "s": [13.5]}, {"t": 87, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 88, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 92, "s": [13.5]}, {"t": 97, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 98, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 102, "s": [13.5]}, {"t": 107, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 108, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 112, "s": [13.5]}, {"t": 117, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 122, "s": [13.5]}, {"t": 127, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 128, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 132, "s": [13.5]}, {"t": 137, "s": [0], "h": 1}]}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Shape 1", "bm": 0, "hd": false}], "ip": 68, "op": 138, "st": 4, "bm": 0}, {"ddd": 0, "ind": 70, "ty": 4, "nm": "speedmid", "parent": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-251, 122, 0]}, "a": {"a": 0, "k": [-251, 42, 0]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-251, 42], [249.5, 42]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 68, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 75, "s": [100]}, {"t": 77, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 78, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 85, "s": [100]}, {"t": 87, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 88, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 95, "s": [100]}, {"t": 97, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 98, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105, "s": [100]}, {"t": 107, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 108, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 115, "s": [100]}, {"t": 117, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 125, "s": [100]}, {"t": 127, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 128, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 135, "s": [100]}, {"t": 137, "s": [100], "h": 1}]}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 68, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 70, "s": [0]}, {"t": 77, "s": [100], "h": 1}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 78, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 80, "s": [0]}, {"t": 87, "s": [100], "h": 1}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 88, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 90, "s": [0]}, {"t": 97, "s": [100], "h": 1}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 98, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 100, "s": [0]}, {"t": 107, "s": [100], "h": 1}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 108, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 110, "s": [0]}, {"t": 117, "s": [100], "h": 1}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 118, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 120, "s": [0]}, {"t": 127, "s": [100], "h": 1}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 128, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.333], "y": [0]}, "t": 130, "s": [0]}, {"t": 137, "s": [100], "h": 1}]}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 35}, "w": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 68, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 72, "s": [13.5]}, {"t": 77, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 78, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 82, "s": [13.5]}, {"t": 87, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 88, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 92, "s": [13.5]}, {"t": 97, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 98, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 102, "s": [13.5]}, {"t": 107, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 108, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 112, "s": [13.5]}, {"t": 117, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 118, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 122, "s": [13.5]}, {"t": 127, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 128, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 132, "s": [13.5]}, {"t": 137, "s": [0], "h": 1}]}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Shape 1", "bm": 0, "hd": false}], "ip": 68, "op": 138, "st": 4, "bm": 0}, {"ddd": 0, "ind": 71, "ty": 4, "nm": "Orange 5", "sr": 1, "ks": {"r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110, "s": [153.622]}, {"t": 120, "s": [0]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [8.619, 364.069, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 120, "s": [505.619, 364.069, 0]}]}, "a": {"a": 0, "k": [408.619, 305.069, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 110, "s": [0, 70, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0, 0]}, "t": 115, "s": [70, 70, 100]}, {"t": 120, "s": [0, 70, 100]}]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[7.797, 17.366], [21.619, -17.012], [4.253, 7.443], [10.012, -37.961], [0, 0], [-33.33, -22.505], [-16.657, 21.973], [7.443, 1.772], [-4.253, 35.086], [5.234, -0.688]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-14.937, 10.732], [25.661, 17.327], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.312, -52.163], [-3.656, -14.895], [9.432, -53.995], [-39.814, 11.45], [-41.508, -26.276], [-45.96, 63.728], [54.46, 40.843], [1.339, 45.351], [61.521, -20.773], [16.534, 14.58]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.647058844566, 0.168627455831, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 12}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [408.619, 289.995]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-8.566, -11.82], [-26.244, 7.621]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-15.649, -18.769], [-7.51, 9.359], [28.401, 10.794]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.784313738346, 0.235294118524, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [380.044, 323.943]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-7.011, -18.996], [-23.921, 4.513]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-23.335, -5.96], [-22.238, 27.939], [14.774, 27.939]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.784313738346, 0.235294118524, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [417.745, 277.845]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-28.618, 19.723]], "o": [[14.865, -22.894], [0, 0]], "v": [[-29.905, 36.721], [36.75, -36.218]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.784313738346, 0.235294118524, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [389.458, 315.289]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [389.458, 315.289]}, "a": {"a": 0, "k": [389.458, 315.289]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 30}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[7.797, 17.366], [21.619, -17.012], [4.253, 7.443], [5.316, -32.96], [0, 0], [-33.33, -22.505], [-16.657, 21.973], [7.443, 1.772], [-4.253, 35.086], [5.234, -0.688]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-14.938, 10.732], [25.661, 17.327], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.312, -52.163], [6.579, -14.29], [8.153, -53.995], [-41.094, 12.055], [-43.427, -25.671], [-45.96, 63.728], [53.18, 41.448], [-2.5, 44.746], [62.8, -23.8], [21.651, 15.185]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.443137288094, 0.207843154669, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [408.619, 289.995]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-11.711, 18.036]], "o": [[0, 0], [0, 0]], "v": [[30.316, 4.852], [31.949, -39.321]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.647058844566, 0.168627455831, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [327.605, 391.332]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5", "bm": 0, "hd": false}], "ip": 110, "op": 121, "st": 41, "bm": 0}, {"ddd": 0, "ind": 72, "ty": 4, "nm": "Orange 3", "sr": 1, "ks": {"r": {"a": 0, "k": 181.395}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [8.619, 359.069, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 103, "s": [505.619, 359.069, 0]}]}, "a": {"a": 0, "k": [408.619, 305.069, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 93, "s": [0, 70, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0, 0]}, "t": 98, "s": [70, 70, 100]}, {"t": 103, "s": [0, 70, 100]}]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[7.797, 17.366], [21.619, -17.012], [4.253, 7.443], [10.012, -37.961], [0, 0], [-33.33, -22.505], [-16.657, 21.973], [7.443, 1.772], [-4.253, 35.086], [5.234, -0.688]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-14.937, 10.732], [25.661, 17.327], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.312, -52.163], [-3.656, -14.895], [9.432, -53.995], [-39.814, 11.45], [-41.508, -26.276], [-45.96, 63.728], [54.46, 40.843], [1.339, 45.351], [61.521, -20.773], [16.534, 14.58]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.647058844566, 0.168627455831, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 12}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [408.619, 289.995]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-8.566, -11.82], [-26.244, 7.621]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-15.649, -18.769], [-7.51, 9.359], [28.401, 10.794]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.784313738346, 0.235294118524, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [380.044, 323.943]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-7.011, -18.996], [-23.921, 4.513]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-23.335, -5.96], [-22.238, 27.939], [14.774, 27.939]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.784313738346, 0.235294118524, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [417.745, 277.845]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-28.618, 19.723]], "o": [[14.865, -22.894], [0, 0]], "v": [[-29.905, 36.721], [36.75, -36.218]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.784313738346, 0.235294118524, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [389.458, 315.289]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [389.458, 315.289]}, "a": {"a": 0, "k": [389.458, 315.289]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 30}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[7.797, 17.366], [21.619, -17.012], [4.253, 7.443], [5.316, -32.96], [0, 0], [-33.33, -22.505], [-16.657, 21.973], [7.443, 1.772], [-4.253, 35.086], [5.234, -0.688]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-14.938, 10.732], [25.661, 17.327], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.312, -52.163], [6.579, -14.29], [8.153, -53.995], [-41.094, 12.055], [-43.427, -25.671], [-45.96, 63.728], [53.18, 41.448], [-2.5, 44.746], [62.8, -23.8], [21.651, 15.185]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.443137288094, 0.207843154669, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [408.619, 289.995]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-11.711, 18.036]], "o": [[0, 0], [0, 0]], "v": [[30.316, 4.852], [31.949, -39.321]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.647058844566, 0.168627455831, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [327.605, 391.332]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5", "bm": 0, "hd": false}], "ip": 93, "op": 104, "st": 24, "bm": 0}, {"ddd": 0, "ind": 73, "ty": 4, "nm": "speedfront", "sr": 1, "ks": {"p": {"a": 0, "k": [5, 238.736, 0]}, "a": {"a": 0, "k": [-251, 42, 0]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-251, 42], [249.5, 42]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 65, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 69.666, "s": [100]}, {"t": 71, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 81, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 85.666, "s": [100]}, {"t": 87, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 96, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100.666, "s": [100]}, {"t": 102, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 114.666, "s": [100]}, {"t": 116, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 128, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 132.666, "s": [100]}, {"t": 134, "s": [100], "h": 1}]}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 65, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 68, "s": [0]}, {"t": 71, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 81, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 84, "s": [0]}, {"t": 87, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 96, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 99, "s": [0]}, {"t": 102, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 113, "s": [0]}, {"t": 116, "s": [100], "h": 1}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 128, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 131, "s": [0]}, {"t": 134, "s": [100], "h": 1}]}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "Trim Paths 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 35}, "w": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 65, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 68, "s": [20]}, {"t": 71, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 81, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 84, "s": [20]}, {"t": 87, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 96, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 99, "s": [20]}, {"t": 102, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 113, "s": [20]}, {"t": 116, "s": [0], "h": 1}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 128, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [1], "y": [0]}, "t": 131, "s": [20]}, {"t": 134, "s": [0], "h": 1}]}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Shape 1", "bm": 0, "hd": false}], "ip": 65, "op": 135, "st": 1, "bm": 0}, {"ddd": 0, "ind": 74, "ty": 4, "nm": "Orange", "sr": 1, "ks": {"p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69, "s": [8.619, 330.069, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 79, "s": [505.619, 330.069, 0]}]}, "a": {"a": 0, "k": [408.619, 305.069, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 69, "s": [0, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 74, "s": [60, 60, 100]}, {"t": 79, "s": [0, 100, 100]}]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[7.797, 17.366], [21.619, -17.012], [4.253, 7.443], [10.012, -37.961], [0, 0], [-33.33, -22.505], [-16.657, 21.973], [7.443, 1.772], [-4.253, 35.086], [5.234, -0.688]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-14.937, 10.732], [25.661, 17.327], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.312, -52.163], [-3.656, -14.895], [9.432, -53.995], [-39.814, 11.45], [-41.508, -26.276], [-45.96, 63.728], [54.46, 40.843], [1.339, 45.351], [61.521, -20.773], [16.534, 14.58]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.647058844566, 0.168627455831, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 12}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [408.619, 289.995]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-8.566, -11.82], [-26.244, 7.621]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-15.649, -18.769], [-7.51, 9.359], [28.401, 10.794]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.784313738346, 0.235294118524, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [380.044, 323.943]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-7.011, -18.996], [-23.921, 4.513]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-23.335, -5.96], [-22.238, 27.939], [14.774, 27.939]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.784313738346, 0.235294118524, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [417.745, 277.845]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 2", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-28.618, 19.723]], "o": [[14.865, -22.894], [0, 0]], "v": [[-29.905, 36.721], [36.75, -36.218]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.784313738346, 0.235294118524, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [389.458, 315.289]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [389.458, 315.289]}, "a": {"a": 0, "k": [389.458, 315.289]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 30}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 1", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[7.797, 17.366], [21.619, -17.012], [4.253, 7.443], [5.316, -32.96], [0, 0], [-33.33, -22.505], [-16.657, 21.973], [7.443, 1.772], [-4.253, 35.086], [5.234, -0.688]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-14.938, 10.732], [25.661, 17.327], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[48.312, -52.163], [6.579, -14.29], [8.153, -53.995], [-41.094, 12.055], [-43.427, -25.671], [-45.96, 63.728], [53.18, 41.448], [-2.5, 44.746], [62.8, -23.8], [21.651, 15.185]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.443137288094, 0.207843154669, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "Fill 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [408.619, 289.995]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 4", "bm": 0, "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-11.711, 18.036]], "o": [[0, 0], [0, 0]], "v": [[30.316, 4.852], [31.949, -39.321]], "c": false}}, "nm": "Path 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.647058844566, 0.168627455831, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 13}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [327.605, 391.332]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 5", "bm": 0, "hd": false}], "ip": 69, "op": 80, "st": 0, "bm": 0}, {"ddd": 0, "ind": 75, "ty": 4, "nm": "glow 13", "parent": 64, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 70, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 74, "s": [75]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 75, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 79, "s": [75]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 80, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 84, "s": [75]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 85, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 89, "s": [75]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 94, "s": [75]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 95, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 99, "s": [75]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 100, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 104, "s": [75]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 109, "s": [75]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 110, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 114, "s": [75]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 115, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 119, "s": [75]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 120, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 124, "s": [75]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 125, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 131, "s": [100]}, {"t": 149, "s": [0]}]}, "r": {"a": 0, "k": 221.325}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [146.766, 376.328, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 149, "s": [142.638, 336.532, 0]}]}, "a": {"a": 0, "k": [287.865, 216.468, 0]}, "s": {"a": 0, "k": [123.811, 147.704, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, -78.273], [-83.593, 0], [0, 78.273], [83.593, 0]], "o": [[0, 78.273], [83.593, 0], [0, -78.273], [-83.593, 0]], "v": [[-151.359, 0], [0, 141.726], [151.359, 0], [0, -141.726]], "c": true}}, "nm": "Path 1", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.945, 0.882, 0.204, 0.762, 0.945, 0.882, 0.204, 1, 0.945, 0.882, 0.204, 0.001, 1, 0.736, 0.5, 1, 0]}}, "s": {"a": 0, "k": [0.432, 4.152]}, "e": {"a": 0, "k": [-93.643, -103.027]}, "t": 2, "h": {"a": 0, "k": 0}, "a": {"a": 0, "k": 0}, "nm": "35[pspksodigksgd", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [287.865, 216.468]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "Transform"}], "nm": "Group 3", "bm": 0, "hd": false}], "ip": 70, "op": 150, "st": 2, "bm": 0}]}